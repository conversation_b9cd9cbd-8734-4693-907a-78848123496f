$(function () {

    $("#IndicadorEconomicoTipoFilter :input").on('input', function () {
        dataTable.ajax.reload();
    });

    const getFilter = function () {
        const input = {};
        $("#IndicadorEconomicoTipoFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/IndicadorEconomicoTipoFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.indicadorEconomicoTipos.indicadorEconomicoTipo;
    const detalheModal = new abp.ModalManager(abp.appPath + 'IndicadorEconomicoTipos/DetalheModal');
    const createModal = new abp.ModalManager(abp.appPath + 'IndicadorEconomicoTipos/CreateModal');
    const editModal = new abp.ModalManager(abp.appPath + 'IndicadorEconomicoTipos/EditModal');

    const dataTable = $('#IndicadorEconomicoTipoTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: false,
        scrollCollapse: true,
        order: [[1, "asc"]],
        ajax: abp.libs.datatables.createAjax(service.getList,getFilter),
        columnDefs: [
            {
                rowAction: {
                    items:
                        [
                            {
                                text: "Detalhe",
                                action: function (data) {
                                    detalheModal.open({ id: data.record.tipoIndicadorEconomicoId});
                                }
                            }
                             ,
                            {
                                text: "Alterar",
                                action: function (data) {
                                    editModal.open({ id: data.record.tipoIndicadorEconomicoId});
                                }
                            },
                            {
                                text: "Excluir",
                                confirmMessage: function () {
                                    return "Tem certeza de que deseja excluir?"
                                },
                                action: function (data) {
                                    abp.ui.block({ elm: 'body', busy: true });
                                    service.delete(data.record.tipoIndicadorEconomicoId)
                                        .then(function () {
                                            abp.notify.info('Excluído com sucesso!');
                                            dataTable.ajax.reload();
                                        }).always(function () { abp.ui.unblock(); });
                                }
                            }                        ]
                }
            },
            {
                title: "Id Tipo",
                data: "tipoIndicadorEconomicoId"
            },
            {
                title: "Código Tipo",
                data: "codigo"
            },
            {
                title: "Descrição Tipo",
                data: "descricao"
            },
            {
                title: "ATIVO",
                data: "ativo",
                render: function (ativo, type, row, meta) {
                    if (ativo) {
                        return "SIM"
                    }

                    return "NÃO"
                }
            }
        ]
    }));
    createModal.onResult(function () {
        dataTable.ajax.reload();
    });

    editModal.onResult(function () {
        dataTable.ajax.reload();
    });

    $('#NewIndicadorEconomicoTipoButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });
});
