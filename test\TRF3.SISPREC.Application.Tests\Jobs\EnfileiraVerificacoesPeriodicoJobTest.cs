using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using Quartz;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.RequisicoesVerificacoes;
using TRF3.SISPREC.RequisicoesVerificacoes.Jobs;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.SettingManagement;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.Application.Tests.Jobs;

public class EnfileiraVerificacoesPeriodicoJobTest
{
    private readonly IGetLoggerService _loggerService;
    private readonly ILogger _logger;
    private readonly IScheduler _scheduler;
    private readonly IBackgroundJobManager _backgroundJobManager;
    private readonly IRequisicaoVerificacaoRepository _requisicaoVerificacaoRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly ISettingManager _settingManager;
    private readonly EnfileiraVerificacoesPeriodicoJob _job;

    public EnfileiraVerificacoesPeriodicoJobTest()
    {
        _logger = Substitute.For<ILogger>();
        _loggerService = Substitute.For<IGetLoggerService>();
        _loggerService.Logger.Returns(_logger);
        _scheduler = Substitute.For<IScheduler>();
        _backgroundJobManager = Substitute.For<IBackgroundJobManager>();
        _requisicaoVerificacaoRepository = Substitute.For<IRequisicaoVerificacaoRepository>();
        _unitOfWorkManager = Substitute.For<IUnitOfWorkManager>();
        _settingManager = Substitute.For<ISettingManager>();

        _job = new EnfileiraVerificacoesPeriodicoJob(
            _loggerService,
            _scheduler,
            _backgroundJobManager,
            _requisicaoVerificacaoRepository,
            _unitOfWorkManager,
            _settingManager
        );
    }

    [Fact]
    public async Task Execute_Deve_Enfileirar_Jobs_Quando_Configuracoes_De_Verificacao_Estiverem_Ativas()
    {
        // Arrange
        var context = Substitute.For<IJobExecutionContext>();
        var uow = Substitute.For<IUnitOfWork>();

        var nomeConfiguracao = VerificacaoRequisicoesSettings.ObterNomeConfiguracaoPorTipoVerificacao(EVerificacaoRequisicaoTipo.VERIFICACAO_CPF_CNPJ);

        var verificacoes = new List<RequisicaoVerificacao>
        {
            new RequisicaoVerificacao { VerificacaoTipoId = (int)EVerificacaoRequisicaoTipo.VERIFICACAO_CPF_CNPJ, Executado = false }
        };

        _unitOfWorkManager.Begin(true, true).Returns(uow);
        var verificacoesLista = verificacoes.AsQueryable().BuildMockDbSet();
        _requisicaoVerificacaoRepository.GetQueryableAsync().Returns(verificacoesLista);
        _settingManager.GetOrNullGlobalAsync(nomeConfiguracao).Returns(Task.FromResult(bool.TrueString));

        // Act
        await _job.Execute(context);

        // Assert
        await _backgroundJobManager.Received(1).EnqueueAsync(Arg.Any<object>());
        await _unitOfWorkManager.Current.Received(1).CompleteAsync();
    }

    [Fact]
    public async Task Execute_Nao_Deve_Enfileirar_Jobs_Quando_Configuracoes_De_Verificacao_Nao_Estiverem_Ativas()
    {
        // Arrange
        var context = Substitute.For<IJobExecutionContext>();
        var uow = Substitute.For<IUnitOfWork>();

        var nomeConfiguracao = VerificacaoRequisicoesSettings.ObterNomeConfiguracaoPorTipoVerificacao(EVerificacaoRequisicaoTipo.VERIFICACAO_CPF_CNPJ);

        var verificacoes = new List<RequisicaoVerificacao>
        {
            new RequisicaoVerificacao { VerificacaoTipoId = (int)EVerificacaoRequisicaoTipo.VERIFICACAO_CPF_CNPJ, Executado = false }
        };

        _unitOfWorkManager.Begin(true, true).Returns(uow);
        var verificacoesLista = verificacoes.AsQueryable().BuildMockDbSet();
        _requisicaoVerificacaoRepository.GetQueryableAsync().Returns(verificacoesLista);
        _settingManager.GetOrNullGlobalAsync(nomeConfiguracao).Returns(Task.FromResult(bool.FalseString));

        // Act
        await _job.Execute(context);

        // Assert
        await _backgroundJobManager.DidNotReceive().EnqueueAsync(Arg.Any<object>());
        await _unitOfWorkManager.Current.Received(1).CompleteAsync();
    }

    [Fact]
    public async Task Execute_Deve_Manipular_OperationCanceledException()
    {
        // Arrange
        var context = Substitute.For<IJobExecutionContext>();
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();
        CancellationToken token = cancellationTokenSource.Token;
        context.CancellationToken.Returns(token);

        // Act
        await _job.Execute(context);

        // Assert
        _logger.Received().Log(
            LogLevel.Warning,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString().Contains("EnfileiraVerificacoesPeriodicoJob foi interrompido.")),
            Arg.Any<OperationCanceledException>(),
            Arg.Any<Func<object, Exception, string>>()
        );
        await _unitOfWorkManager.Current.Received(1).CompleteAsync();
    }

    [Fact]
    public async Task Execute_Deve_Manipular_Excecao_Geral()
    {
        // Arrange
        var context = Substitute.For<IJobExecutionContext>();
        var uow = Substitute.For<IUnitOfWork>();

        _unitOfWorkManager.Begin(true, true).Returns(uow);
        _requisicaoVerificacaoRepository.GetQueryableAsync().Returns(Task.FromException<IQueryable<RequisicaoVerificacao>>(new Exception("Test Exception")));

        // Act
        await _job.Execute(context);

        // Assert
        _logger.Received().Log(
             LogLevel.Error,
             Arg.Any<EventId>(),
             Arg.Is<object>(o => o.ToString().Contains("Erro ao executar EnfileiraVerificacoesPeriodicoJob.")),
             Arg.Any<Exception>(),
             Arg.Any<Func<object, Exception, string>>()
         );
        await _unitOfWorkManager.Current.Received(1).CompleteAsync();
    }

}
