<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RootNamespace>TRF3.SISPREC</RootNamespace>
		<SonarQubeExclude>true</SonarQubeExclude>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="AnalisePendencias\**" />
	  <EmbeddedResource Remove="AnalisePendencias\**" />
	  <None Remove="AnalisePendencias\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
		<PackageReference Include="MockQueryable.EntityFrameworkCore" Version="7.0.1" />
		<PackageReference Include="MockQueryable.NSubstitute" Version="7.0.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\TRF3.SISPREC.Application.Contracts\TRF3.SISPREC.Application.Contracts.csproj" />
		<ProjectReference Include="..\..\src\TRF3.SISPREC.Domain\TRF3.SISPREC.Domain.csproj" />
		<ProjectReference Include="..\..\src\TRF3.SISPREC.ProcessaPrecatorio.Domain\TRF3.SISPREC.ProcessaPrecatorio.Domain.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.TestBase\TRF3.SISPREC.TestBase.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
	</ItemGroup>

	<ItemGroup>
		<None Update="xunit.runner.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
