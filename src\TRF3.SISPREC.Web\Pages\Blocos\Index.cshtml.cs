using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.Blocos;

public class IndexModel : SISPRECPageModel
{
    public BlocosFilterInput BlocosFilter { get; set; } = new();

    public virtual async Task OnGetAsync()
    {
        await Task.CompletedTask;
    }
}

public class BlocosFilterInput
{
    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Nº Bloco")]
    public int? NumeroBloco { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Data Criação Início")]
    public DateTime? DataCriacaoInicio { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Data Criação Fim")]
    public DateTime? DataCriacaoFim { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Usuário")]
    public string? Usuario { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Status")]
    public EStatusBloco? Status { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Nº Expediente")]
    public int? NumeroExpediente { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Requisição")]
    public string? Requisicao { get; set; }

}
