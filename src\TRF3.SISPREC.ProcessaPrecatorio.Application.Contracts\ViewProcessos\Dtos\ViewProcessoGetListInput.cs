using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.ViewProcessos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class ViewProcessoGetListInput : PagedAndSortedResultRequestDto
{
    //[Display(Name = "Id")]
    public int? ControleId { get; set; }
    public int FaseId { get; set; }

    [Display(Name = "Número do Processo")]
    public string? NumeroProcesso { get; set; }

    [Display(Name = "Status")]
    public EControleProcessamentoProcessoStatus? Status { get; set; }

}