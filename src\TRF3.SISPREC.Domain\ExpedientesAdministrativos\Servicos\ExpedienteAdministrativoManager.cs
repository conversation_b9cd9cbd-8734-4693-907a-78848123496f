using TRF3.SISPREC.BlocosSisprec;
using TRF3.SISPREC.Domain;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.SincronizacaoLegado;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace TRF3.SISPREC.ExpedientesAdministrativos.Servicos
{
    [ExposeServices(typeof(IExpedienteAdministrativoManager))]
    public class ExpedienteAdministrativoManager : BaseDomainManager<ExpedienteAdministrativo>, IExpedienteAdministrativoManager
    {
        #region Read-Only Fields

        private readonly IReqPagUnitOfWork _reqPagUnitOfWork;
        private readonly IBlocoSisprecRepository _blocoSisprecRepository;
        private readonly ICurrentUser _currentUser;

        #endregion

        #region Constructors

        public ExpedienteAdministrativoManager(
                IRepository<ExpedienteAdministrativo> repository,
                IReqPagUnitOfWork reqPagUnitOfWork,
                IBlocoSisprecRepository blocoSisprecRepository,
                ICurrentUser currentUser
            ) : base(repository)
        {
            _reqPagUnitOfWork = reqPagUnitOfWork ?? throw new ArgumentNullException(nameof(reqPagUnitOfWork));
            _blocoSisprecRepository = blocoSisprecRepository ?? throw new ArgumentNullException(nameof(blocoSisprecRepository));
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
        }

        #endregion

        #region IExpedienteAdministrativoManager Members

        public async Task GerarBlocoAsync(ExpedienteAdministrativo entidade)
        {
            var bloco = await _blocoSisprecRepository.InsertAsync(new BlocoSisprec
            {
                DataCriacao = DateTime.Now,
                NomeUsuario = _currentUser.UserName.ToString(),
                StatusBloco = EStatusBloco.GERADO
            }, true);
            entidade.BlocoSisprecId = bloco.BlocoSisprecId;
            await _repository.UpdateAsync(entidade);
        }

        public async Task IncluirExpedienteEmBlocoExistenteAsync(int expedienteId, int blocoId)
        {
            var expedienteAdministrativo = await _repository.FirstOrDefaultAsync(x => x.ExpedienteAdministrativoId == expedienteId)
                 ?? throw new ArgumentException(nameof(expedienteId));

            expedienteAdministrativo.BlocoSisprecId = blocoId;
            await _repository.UpdateAsync(expedienteAdministrativo);
        }

        public async override Task<ExpedienteAdministrativo> InserirAsync(ExpedienteAdministrativo entidade, bool autoSave = false, CancellationToken cancellationToken = default)
        {
            var lastId = _repository.LastOrDefaultAsync().Id;

            int nextIncremental = lastId == 0 ? 1 : int.Parse(lastId.ToString()) + 1;
            int year = DateTime.Now.Year;
            entidade.NumeroExpedienteAdministrativo = int.Parse($"{year}{nextIncremental:D6}");

            return await base.InserirAsync(entidade, autoSave, cancellationToken);
        }

        public async Task<ExpedienteAdministrativo> InserirImportacaoAsync(int numeroExpedienteAdministrativo)
        {

            var expedienteAdministrativo = await _repository.FirstOrDefaultAsync(x => x.NumeroExpedienteAdministrativo == numeroExpedienteAdministrativo);
            if (expedienteAdministrativo != null)
                return expedienteAdministrativo;

            var expedienteAdministrativosreqpag = _reqPagUnitOfWork.ExpedienteAdministrativoRepository.BuscaPorCodigoLista(numeroExpedienteAdministrativo).Result.FirstOrDefault();
            var expedienteAdministrativos = new ExpedienteAdministrativo()
            {
                DataExpedienteAdministrativo = expedienteAdministrativosreqpag?.dat_expedi_admini ?? default(DateTime),
                NomeUsuario = expedienteAdministrativosreqpag?.cod_usuari ?? "",
                NumeroExpedienteAdministrativo = expedienteAdministrativosreqpag?.num_expedi_admini ?? 0,
                ObservacaoExpedienteAdministrativo = expedienteAdministrativosreqpag?.des_observ_expedi_admini ?? "",
                TipoExpedienteAdministrativo = ETipoExpedienteAdministrativoHelper.Parse(expedienteAdministrativosreqpag?.tip_expedi_admini ?? "O")
            };

            return await base.InserirAsync(expedienteAdministrativos);
        }

        #endregion
    }
}
