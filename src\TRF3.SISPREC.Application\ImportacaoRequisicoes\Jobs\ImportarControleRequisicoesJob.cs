using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.RequisicoesProtocolos;
using TRF3.SISPREC.Settings.Importacoes;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Threading;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.ImportacaoRequisicoes.Jobs;

[ExcludeFromCodeCoverage]
public class ImportarControleRequisicoesJob : AsyncBackgroundJob<ImportacaoControleArgs>, IImportarControleRequisicoesJob
{

    private readonly ICancellationTokenProvider _cancelationToken;
    private readonly IImportarRequisicoesService _controleService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public ImportarControleRequisicoesJob(IImportarRequisicoesService controleService,
                                          ICancellationTokenProvider cancellationTokenProvider,
                                          IUnitOfWorkManager unitOfWorkManager)
    {
        _controleService = controleService ?? throw new ArgumentNullException(nameof(controleService));
        _cancelationToken = cancellationTokenProvider ?? throw new ArgumentNullException(nameof(cancellationTokenProvider));
        _unitOfWorkManager = unitOfWorkManager;
    }

    public override async Task ExecuteAsync(ImportacaoControleArgs args)
    {
        try
        {
            long propostaId = args.PropostaId;
            if (propostaId <= 0 || _cancelationToken.Token.IsCancellationRequested)
                return;

            using (var uow = _unitOfWorkManager.Begin(new AbpUnitOfWorkOptions(isTransactional: true, timeout: 2 * 60 * 1000), requiresNew: true))
            {
                await _controleService.ImportarParaControle(propostaId);
                await uow.CompleteAsync();
            }
        }
        catch (OperationCanceledException)
        {
            Logger.LogWarning("Job ImportarControleRequisicoesJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao inserir registro de controle de importação requisição: PropostaId: {PropostaId}. Exceção: {Excecao} - Message: {Message}", args.PropostaId, nameof(ex), ex.Message);
        }
    }
}

[BackgroundJobName("ImportaControle")]
[ExcludeFromCodeCoverage]
public class ImportacaoControleArgs : BaseBackgroundJobArgs
{
    public long PropostaId { get; set; }

    public override string JobGroupName => ImportacaoRequisicoesSettings.ImportarControleRequisicoesJobGroupName;

    public ImportacaoControleArgs(long propostaId)
    {
        PropostaId = propostaId;
    }
}