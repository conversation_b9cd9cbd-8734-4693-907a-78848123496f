using System.ComponentModel;
using TRF3.SISPREC.Assuntos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.RequisicaoEstornos;
using TRF3.SISPREC.RequisicaoObservacoes;
using TRF3.SISPREC.RequisicoesVerificacoes;
using TRF3.SISPREC.REquisicoesExpedientesAdministrativos;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesPartes;
using TRF3.SISPREC.RequisicoesPlanosOrcamentos;
using TRF3.SISPREC.RequisicoesProcessosOrigens;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos.AssuntosExecucoes;
using TRF3.SISPREC.SituacoesRequisicoesProtocolos;
using TRF3.SISPREC.TiposProcedimentos;
using TRF3.SISPREC.UnidadesJudiciais;
using Volo.Abp;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicoesDocumentos;

namespace TRF3.SISPREC.RequisicoesProtocolos
{
    [Audited]
    public class RequisicaoProtocolo : Entity, ISoftDelete
    {
        public override object[] GetKeys()
        {
            return new object[] { NumeroProtocoloRequisicao };
        }

        [DisplayName("ID Unidade")]
        public int? UnidadeJudicialId { get; set; }

        [DisplayName("Nº Ofício")]
        public string NumeroOficioRequisitorio { get; set; }

        [DisplayName("Status Protocolo")]
        public EStatusProtocoloRequisicao StatusProtocoloRequisicao { get; set; } = EStatusProtocoloRequisicao.RECEBIDA;

        [DisplayName("Data/Hora Protocolo")]
        public DateTime DataHoraProtocoloRequisicao { get; set; }

        [DisplayName("ID Identificador")]
        public EIdentificadorRequisicaoProtocolo IdentificadorProtocoloRequisicao { get; set; }

        [DisplayName("Indicador Inclusão")]
        public EIndicadorInclusaoRequisicao? IndicadorInclusaoRequisicao { get; set; }

        [DisplayName("Indicador Estorno")]
        public EIndicadorEstornoRequisicao? IndicadorEstornoRequisicao { get; set; }

        [DisplayName("Nº Protocolo Único")]
        public string NumeroProtocoloRequisicaoUnica { get; set; }

        [DisplayName("Nº Protocolo")]
        public string NumeroProtocoloRequisicao { get; set; }

        [DisplayName("ID Assunto")]
        public int AssuntoId { get; set; }

        [DisplayName("ID Tipo Proced.")]
        public string TipoProcedimentoId { get; set; }

        [DisplayName("Tipo Honorário")]
        public ETipoHonorario TipoHonorario { get; set; }

        [DisplayName("Renúncia Limite")]
        public bool RenunciaValorLimite { get; set; }

        [DisplayName("Valor Requisição")]
        public decimal ValorRequisicao { get; set; }

        [DisplayName("Data Conta")]
        public DateTime DataContaLiquidacao { get; set; }

        [DisplayName("Tipo Requisição")]
        public ETipoRequisicao TipoRequisicao { get; set; }

        [DisplayName("Valor Conta")]
        public decimal? ValorConta { get; set; }

        [DisplayName("Data Conta")]
        public DateTime? DataConta { get; set; }

        [DisplayName("Data Trânsito Julgado")]
        public DateTime? DataTransitoJulgadoFase { get; set; }

        [DisplayName("Data Trânsito Embargos")]
        public DateTime? DataTransitoJulgadoEmbargos { get; set; }

        [DisplayName("Nome Magistrado")]
        public string NomeMagistrado { get; set; }

        [DisplayName("Designação Magistrado")]
        public EDesignacaoMagistrado DesignacaoMagistrado { get; set; }

        [DisplayName("Natureza Crédito")]
        public ENaturezaCredito NaturezaCredito { get; set; }

        [DisplayName("Desapropriação Único")]
        public bool DesapropriacaoUnicoImovel { get; set; }

        [DisplayName("Valor Atualizado")]
        public decimal ValorAtualizadoRequisicao { get; set; }

        [DisplayName("ID Situação")]
        public int SituacaoRequisicaoId { get; set; }

        [DisplayName("Execução Fiscal")]
        public bool? ExecucaoFiscal { get; set; }

        [DisplayName("Valor Compensação")]
        public decimal? ValorCompensacao { get; set; }

        [DisplayName("Bloqueio Depósito")]
        public bool? BloqueioDepositoJudicial { get; set; }

        [DisplayName("Levantamento Ordem")]
        public bool? LevantamentoOrdemJuizo { get; set; }

        [DisplayName("Data Trânsito Compensação")]
        public DateTime? DataTransitoDeferimentoCompensacao { get; set; }

        [DisplayName("Valor Compensação Atualizado")]
        public decimal? ValorCompensacaoAtualizado { get; set; }

        [DisplayName("Valor Requisição Principal")]
        public decimal? ValorRequisicaoPrincipal { get; set; }

        [DisplayName("Valor Requisição Juros")]
        public decimal? ValorRequisicaoJuros { get; set; }

        [DisplayName("Valor Conta Principal")]
        public decimal? ValorContaPrincipal { get; set; }

        [DisplayName("Valor Conta Juros")]
        public decimal? ValorContaJuros { get; set; }

        [DisplayName("Valor Atualizado Principal")]
        public decimal? ValorAtualizadoRequisicaoPrincipal { get; set; }

        [DisplayName("Valor Atualizado Juros")]
        public decimal? ValorAtualizadoRequisicaoJuros { get; set; }

        [DisplayName("Selic")]
        public bool? Selic { get; set; }

        [DisplayName("Valor Total Referência")]
        public decimal? ValorTotalReferencia { get; set; }

        [DisplayName("Indicador Juros Mora")]
        public EIndicadorJurosMora? IndicadorJurosMora { get; set; }

        [DisplayName("Valor Alíquota Juros")]
        public decimal? ValorAliquotaJurosMora { get; set; }

        [DisplayName("Valor Juros Mora")]
        public decimal? ValorJurosMora { get; set; }
        public bool IsDeleted { get; set; } = false;


        #region Virtual Properties

        [DisableAuditing]
        public virtual Assunto Assunto { get; set; }
        [DisableAuditing]
        public virtual TipoProcedimento TipoProcedimento { get; set; }
        [DisableAuditing]
        public virtual SituacaoRequisicaoProtocolo SituacaoRequisicaoProtocolo { get; set; }
        [DisableAuditing]
        public virtual RequisicaoObservacao RequisicaoObservacao { get; set; }
        [DisableAuditing]
        public virtual ICollection<RequisicaoProcessoOrigem>? RequisicaoProcessosOrigem { get; set; }
        [DisableAuditing]
        public virtual ICollection<AssuntoExecucao>? AssuntoExecucoes { get; set; }
        [DisableAuditing]
        public virtual ICollection<RequisicaoOcorrencia>? RequisicaoOcorrencia { get; set; }
        [DisableAuditing]
        public virtual ICollection<RequisicaoVerificacao> RequisicaoVerificacao { get; set; }
        [DisableAuditing]
        public virtual ICollection<RequisicaoParte> RequisicaoPartes { get; set; }
        [DisableAuditing]
        public virtual UnidadeJudicial? UnidadeJudicial { get; set; }
        [DisableAuditing]
        public virtual ICollection<RequisicaoExpedienteAdministrativo>? RequisicaoExpedienteAdministrativo { get; set; }
        [DisableAuditing]
        public virtual RequisicaoProposta? RequisicaoProposta { get; set; }
        [DisableAuditing]
        public virtual RequisicaoPlanoOrcamento? RequisicaoPlanoOrcamento { get; set; }
        [DisableAuditing]
        public virtual RequisicaoEstorno? RequisicaoEstorno { get; set; }
        [DisableAuditing]
        public virtual ICollection<RequisicaoJustificativa>? RequisicaoJustificativa { get; set; }
        [DisableAuditing]
        public virtual ICollection<RequisicaoDocumento>? RequisicaoDocumentos { get; set; }

        #endregion
    }
}