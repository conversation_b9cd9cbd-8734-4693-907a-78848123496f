using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using TRF3.SISPREC.RequisicoesVerificacoes.Jobs;
using Volo.Abp.SettingManagement;

namespace TRF3.SISPREC.Tests.Settings.VerificacaoRequisicoes;


public class VerificacaoRequisicoesSettingsAppServiceTests : BaseAppServiceTests<SISPRECApplicationTestModule>
{
    private IBackgroundJobsService _backgroundJobsService;
    private ISettingManager _settingManager;
    private IVerificacaoRequisicoesSettingsAppService _appService;
    private IEnfileiraVerificacoesPeriodicoJob _enfileiraConsultaCnpjCpfPeriodicoJob;

    public VerificacaoRequisicoesSettingsAppServiceTests()
    {
        _appService = GetRequiredService<IVerificacaoRequisicoesSettingsAppService>();
    }

    protected override void AfterAddApplication(IServiceCollection services)
    {
        services.RemoveAll(typeof(IBackgroundJobsService));
        _backgroundJobsService = Substitute.For<IBackgroundJobsService>();
        services.AddTransient(typeof(IBackgroundJobsService), _ => _backgroundJobsService);

        services.RemoveAll(typeof(ISettingManager));
        _settingManager = Substitute.For<ISettingManager>();
        services.AddTransient(typeof(ISettingManager), _ => _settingManager);

        _enfileiraConsultaCnpjCpfPeriodicoJob = Substitute.For<IEnfileiraVerificacoesPeriodicoJob>();
        services.AddTransient(typeof(IEnfileiraVerificacoesPeriodicoJob), _ => _enfileiraConsultaCnpjCpfPeriodicoJob);

        base.AfterAddApplication(services);
    }

    [Fact]
    public async Task DesabilitarVerificacaoCpfCnpj_Deve_Chamar_PauseJobsAsync_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoCpfCnpjJob(false);

        // Assert
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.ConsultaCnpjCpfJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoCpfCnpjAtiva, false);
    }

    [Fact]
    public async Task HabilitarVerificacaoCpfCnpj_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoCpfCnpjJob(true);

        // Assert
        _enfileiraConsultaCnpjCpfPeriodicoJob.Received(1).Agendar();
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.ConsultaCnpjCpfJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoCpfCnpjAtiva, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoComplementarIgual_Deve_Chamar_PauseJobsAsync_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoComplementarIgualJob(false);

        // Assert
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoComplementarIgualJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoComplementarIgualAtiva, false);
    }

    [Fact]
    public async Task HabilitarVerificacaoComplementarIgual_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoComplementarIgualJob(true);

        // Assert
        _enfileiraConsultaCnpjCpfPeriodicoJob.Received(1).Agendar();
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoComplementarIgualJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoComplementarIgualAtiva, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoSuplementarIgual_Deve_Chamar_PauseJobsAsync_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoSuplementarIgualJob(false);

        // Assert
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualAtiva, false);
    }

    [Fact]
    public async Task HabilitarVerificacaoSuplementarIgual_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoSuplementarIgualJob(true);

        // Assert
        _enfileiraConsultaCnpjCpfPeriodicoJob.Received(1).Agendar();
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualAtiva, true);
    }

    [Fact]
    public async Task HabilitarVerificacaoPrevencaoTipo31_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPrevencaoTipo31Job(true);

        // Assert
        _enfileiraConsultaCnpjCpfPeriodicoJob.Received(1).Agendar();
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31JobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31Ativa, true);
    }

    [Fact]
    public async Task HabilitarVerificacaoPrevencaoTipo34_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPrevencaoTipo34Job(true);

        // Assert
        _enfileiraConsultaCnpjCpfPeriodicoJob.Received(1).Agendar();
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34JobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34Ativa, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoIncontroversoMenorIgual_Deve_Chamar_PauseJobsAsync_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoIncontroversoMenorIgualJob(false);

        // Assert
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualAtiva, false);
    }

    [Fact]
    public async Task HabilitarVerificacaoIncontroversoMenorIgual_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoIncontroversoMenorIgualJob(true);

        // Assert
        _enfileiraConsultaCnpjCpfPeriodicoJob.Received(1).Agendar();
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualAtiva, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoPeritoAdvogado_Deve_Chamar_PauseJobsAsync_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPeritoAdvogadoJob(false);

        // Assert
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoAtiva, false);
    }

    [Fact]
    public async Task DesabilitarVerificacaoPrevencaoTipo31_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPrevencaoTipo31Job(false);

        // Assert
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31JobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31Ativa, false);
    }

    [Fact]
    public async Task DesabilitarVerificacaoPrevencaoTipo34_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPrevencaoTipo34Job(false);

        // Assert
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34JobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34Ativa, false);
    }

    [Fact]
    public async Task HabilitarVerificacaoPeritoAdvogado_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPeritoAdvogadoJob(true);

        // Assert
        _enfileiraConsultaCnpjCpfPeriodicoJob.Received(1).Agendar();
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoAtiva, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoPeritoCnpj_Deve_Chamar_PauseJobsAsync_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPeritoCnpjJob(false);

        // Assert
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjAtiva, false);
    }

    [Fact]
    public async Task HabilitarVerificacaoPeritoCnpj_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPeritoCnpjJob(true);

        // Assert
        _enfileiraConsultaCnpjCpfPeriodicoJob.Received(1).Agendar();
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjAtiva, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoNomeParte_Deve_Chamar_PauseJobsAsync_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoNomeParteJob(false);

        // Assert
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).PausarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoNomeParteJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoNomePartesAtiva, false);
    }

    [Fact]
    public async Task HabilitarVerificacaoNomeParte_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoNomeParteJob(true);

        // Assert
        _enfileiraConsultaCnpjCpfPeriodicoJob.Received(1).Agendar();
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoNomeParteJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoNomePartesAtiva, true);
    }

    [Fact]
    public void ObterConfiguracaoCpfCnpj_Deve_Retornar_Valor_Do_SettingManager()
    {
        // Arrange
        var configKey = "TestConfigKey";
        _settingManager.GetOrNullGlobalAsync(configKey).Returns(Task.FromResult("true"));

        // Act
        var resultado = _appService.IsConfiguracaoAtiva(configKey);

        // Assert
        resultado.ShouldBeTrue();
        _settingManager.Received(1).GetOrNullGlobalAsync(configKey);
    }

    [Fact]
    public async Task DesabilitarVerificacaoAjuizamento_Deve_Desabilitar_Corretamente()
    {
        //Act
        await _appService.AlterarVerificacaoAjuizamentoJob(false);

        //Assert
        await _backgroundJobsService.Received(2).PausarJobsAsync(Arg.Any<string>());
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoAjuizamentoAtiva, false);
    }

    [Fact]
    public async Task HabilitarVerificacaoAjuizamento_Deve_Chamar_ResumeJobsAsync_e_Agendar_Jobs_e_SetGlobalBoolAsync()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoAjuizamentoJob(true);

        // Assert
        _enfileiraConsultaCnpjCpfPeriodicoJob.Received(1).Agendar();
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        await _backgroundJobsService.Received(1).RetomarJobsAsync(VerificacaoRequisicoesSettings.VerificacaoAjuizamentoJobGroupName);
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoAjuizamentoAtiva, true);
    }
    #region VerificacaoPrevencao Tipo21
    [Fact]
    public async Task VerificacaoPrevencaoTipo21_Deve_Retornar_Valor_Do_SettingManager()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPrevencaoTipo21Job(true);

        // Assert 
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo21Ativa, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoPrevencaoTipo21_Deve_Desabilitar_Corretamente()
    {
        //Act
        await _appService.AlterarVerificacaoPrevencaoTipo21Job(false);

        //Assert
        await _backgroundJobsService.Received(2).PausarJobsAsync(Arg.Any<string>());
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo21Ativa, false);
    }
    #endregion

    #region VerificacaoPrevencao Tipo22
    [Fact]
    public async Task VerificacaoPrevencaoTipo22_Deve_Retornar_Valor_Do_SettingManager()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPrevencaoTipo22Job(true);

        // Assert 
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo22Ativa, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoPrevencaoTipo22_Deve_Desabilitar_Corretamente()
    {
        //Act
        await _appService.AlterarVerificacaoPrevencaoTipo22Job(false);

        //Assert
        await _backgroundJobsService.Received(2).PausarJobsAsync(Arg.Any<string>());
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo22Ativa, false);
    }
    #endregion

    #region VerificacaoPrevencao Tipo23
    [Fact]
    public async Task VerificacaoPrevencaoTipo23_Deve_Retornar_Valor_Do_SettingManager()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPrevencaoTipo23Job(true);

        // Assert 
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo23Ativa, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoPrevencaoTipo23_Deve_Desabilitar_Corretamente()
    {
        //Act
        await _appService.AlterarVerificacaoPrevencaoTipo23Job(false);

        //Assert
        await _backgroundJobsService.Received(2).PausarJobsAsync(Arg.Any<string>());
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo23Ativa, false);
    }
    #endregion

    #region VerificacaoPrevencao Tipo24
    [Fact]
    public async Task VerificacaoPrevencaoTipo24_Deve_Retornar_Valor_Do_SettingManager()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPrevencaoTipo24Job(true);

        // Assert 
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo24Ativa, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoPrevencaoTipo24_Deve_Desabilitar_Corretamente()
    {
        //Act
        await _appService.AlterarVerificacaoPrevencaoTipo24Job(false);

        //Assert
        await _backgroundJobsService.Received(2).PausarJobsAsync(Arg.Any<string>());
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo24Ativa, false);
    }
    #endregion

    #region VerificacaoPrevencao Tipo32
    [Fact]
    public async Task VerificacaoPrevencaoTipo32_Deve_Retornar_Valor_Do_SettingManager()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoPrevencaoTipo32Job(true);

        // Assert 
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo32Ativa, true);
    }

    [Fact]
    public async Task DesabilitarVerificacaoPrevencaoTipo32_Deve_Desabilitar_Corretamente()
    {
        //Act
        await _appService.AlterarVerificacaoPrevencaoTipo32Job(false);

        //Assert
        await _backgroundJobsService.Received(2).PausarJobsAsync(Arg.Any<string>());
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo32Ativa, false);
    }
    #endregion

    [Fact]
    public async Task AlterarVerificacaoSucumbencial_Deve_Retornar_Valor_Do_SettingManager()
    {
        // Arrange

        // Act
        await _appService.AlterarVerificacaoSucumbencial(true);

        // Assert 
        await _settingManager.Received(1).SetGlobalBoolAsync(VerificacaoRequisicoesSettings.VerificacaoSucumbencialAtiva, true);
    }
}
