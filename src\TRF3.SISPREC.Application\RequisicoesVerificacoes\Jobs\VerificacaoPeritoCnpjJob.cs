using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using TRF3.SISPREC.VerificacaoPeritoCnpj;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoPeritoCnpjJob : AsyncBackgroundJob<VerificacaoPeritoCnpjJobArgs>, IVerificacaoPeritoCnpjJob
{
    private readonly IVerificacaoPeritoCnpjService _VerificacaoPeritoCnpjService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public VerificacaoPeritoCnpjJob(IVerificacaoPeritoCnpjService verificacaoPeritoCnpjService, IUnitOfWorkManager unitOfWorkManager)
    {
        _VerificacaoPeritoCnpjService = verificacaoPeritoCnpjService;
        _unitOfWorkManager = unitOfWorkManager;
    }

    [ExcludeFromCodeCoverage]
    public override async Task ExecuteAsync(VerificacaoPeritoCnpjJobArgs args)
    {
        try
        {
            if (args.RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar VerificacaoPeritoCnpjJob. RequisicaoVerificacaoId inválido: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
                return;
            }

            if (string.IsNullOrEmpty(args.NumeroProtocoloRequisicao))
            {
                Logger.LogError("Erro ao executar VerificacaoPeritoCnpjJob. NumeroProtocoloRequisicao inválido: NumeroProtocoloRequisicao: {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
                return;
            }

            using IUnitOfWork uow = _unitOfWorkManager.Begin(false, true);
            await _VerificacaoPeritoCnpjService.VerificarPeritoCnpjAsync(args.RequisicaoVerificacaoId, args.NumeroProtocoloRequisicao);
            await uow.CompleteAsync();

        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoPeritoCnpjJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
            throw;
        }
    }
}

[ExcludeFromCodeCoverage]
public class VerificacaoPeritoCnpjJobArgs : BaseBackgroundJobArgs
{
    public long RequisicaoVerificacaoId { get; set; }
    public string NumeroProtocoloRequisicao { get; set; }

    public override string JobGroupName => VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjJobGroupName;

    public VerificacaoPeritoCnpjJobArgs(long requisicaoVerificacaoId, string numeroProtocoloRequisicao)
    {
        RequisicaoVerificacaoId = requisicaoVerificacaoId;
        NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
    }
}