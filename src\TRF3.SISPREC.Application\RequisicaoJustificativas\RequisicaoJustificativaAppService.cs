using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.AcoesJustificativa;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.JustificativaComparacoes;
using TRF3.SISPREC.RequisicaoJustificativas.Dtos;
using TRF3.SISPREC.RequisicoesDocumentos;
using TRF3.SISPREC.RequisicoesDocumentos.Dtos;
using TRF3.SISPREC.RequisicoesPropostas;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.RequisicaoJustificativas;
public class RequisicaoJustificativaAppService :
    BaseCrudAppService<RequisicaoJustificativa, RequisicaoJustificativaDto, long, RequisicaoJustificativaGetListInput, CreateUpdateRequisicaoJustificativaDto, CreateUpdateRequisicaoJustificativaDto>,
    IRequisicaoJustificativaAppService
{

    #region Read-Only Fields

    private readonly IRequisicaoJustificativaManager _manager;
    private readonly IRequisicaoPropostaRepository _requisicaoPropostaRepository;
    private readonly IRequisicaoJustificativaRepository _repository;
    private readonly IJustificativaComparacaoManager _justificativaComparacaoManager;
    private readonly IAcaoJustificativaAppService _acaoJustificativaAppService;
    private readonly IRequisicaoDocumentoAppService _requisicaoDocumentoAppService;
    private readonly IRequisicaoDocumentoManager _requisicaoDocumentoManager;

    #endregion

    #region Constructors

    public RequisicaoJustificativaAppService(IRequisicaoJustificativaRepository repository,
                                             IRequisicaoJustificativaManager manager,
                                             IRequisicaoPropostaRepository requisicaoPropostaRepository,
                                             IJustificativaComparacaoManager justificativaComparacaoManager,
                                             IAcaoJustificativaAppService acaoJustificativaAppService,
                                             IRequisicaoDocumentoAppService requisicaoDocumentoAppService,
                                             IRequisicaoDocumentoManager requisicaoDocumentoManager) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
        _requisicaoPropostaRepository = requisicaoPropostaRepository;
        _justificativaComparacaoManager = justificativaComparacaoManager;
        _acaoJustificativaAppService = acaoJustificativaAppService;
        _requisicaoDocumentoAppService = requisicaoDocumentoAppService;
        _requisicaoDocumentoManager = requisicaoDocumentoManager;
    }

    #endregion

    #region IRequisicaoJustificativaAppService Members

    public async Task<bool> ExisteRequisicao(string numeroProtocoloRequisicao)
    {
        return await _requisicaoPropostaRepository.AnyAsync(x => x.NumeroProtocoloRequisicao == numeroProtocoloRequisicao);
    }

    public async Task<RequisicaoJustificativaDto> CreateJustificativaCompletoAsync(CreateUpdateJustificativaComplementoDto input)
    {
        // Validações
        if (input.RequisicaoList == null || !input.RequisicaoList.Any())
            throw new UserFriendlyException("Não é possível cadastrar justificativa sem requisição.");

        // Data única para todos os registros
        var dataAnalise = Clock.Now;
        var userName = CurrentUser.UserName;

        // Criar justificativa principal
        var primeiraJustificativa = await _manager.InserirAsync(new RequisicaoJustificativa
        {
            AcaoJustificativaId = int.Parse(input.Motivo),
            ComplementoMotivo = input.ComplementoMotivo,
            NomeUsuario = userName,
            AnaliseTelaId = (int)input.TipoAnalise,
            NumeroProtocoloRequisicao = input.RequisicaoList.First(),
            DataAnalise = dataAnalise,
            Observacoes = input.Observacoes
            //autosave=true para RequisicaoJustificativaId estar disponível para criar registros das comparações
        }, true);

        // Criar justificativas adicionais
        foreach (var numeroProtocolo in input.RequisicaoList.Skip(1))
        {
            await _manager.InserirAsync(new RequisicaoJustificativa()
            {
                AcaoJustificativaId = int.Parse(input.Motivo),
                ComplementoMotivo = input.ComplementoMotivo,
                NomeUsuario = userName,
                AnaliseTelaId = (int)input.TipoAnalise,
                NumeroProtocoloRequisicao = numeroProtocolo,
                DataAnalise = dataAnalise,
                Observacoes = input.Observacoes
            });
        }

        // Criar comparações se existirem
        if (input.RequisicoesComparadas?.Any() == true && input.RequisicaoList.Count == 1)
        {
            foreach (var comparacao in input.RequisicoesComparadas)
            {
                var justificativaComparacao = new JustificativaComparacao()
                {
                    NumeroProtocoloRequisicao = comparacao.NumeroRequisicao!,
                    Observacoes = comparacao.Observacoes,
                    RequisicaoJustificativaId = primeiraJustificativa.RequisicaoJustificativaId
                };
                await _justificativaComparacaoManager.InserirAsync(justificativaComparacao);
            }
        }

        return ObjectMapper.Map<RequisicaoJustificativa, RequisicaoJustificativaDto>(primeiraJustificativa);
    }

    public async Task EditarJustificativa(CreateUpdateJustificativaComplementoDto input)
    {
        var justificativa = await _repository.GetAsync(x => x.RequisicaoJustificativaId == input.RequisicaoJustificativaId!.Value);

        var acaoJustificativaId = int.Parse(input.Motivo!);

        var codigoAcaoTipo = justificativa.AcaoJustificativa.AcaoTipo.Codigo;

        bool existeModificacao = false;

        if (justificativa.AcaoJustificativaId != acaoJustificativaId)
        {
            justificativa.AcaoJustificativaId = acaoJustificativaId;
            existeModificacao = true;
        }

        if (!string.Equals(justificativa.ComplementoMotivo, input.ComplementoMotivo))
        {
            justificativa.ComplementoMotivo = input.ComplementoMotivo;
            existeModificacao = true;
        }

        if (!string.Equals(justificativa.Observacoes, input.Observacoes))
        {
            justificativa.Observacoes = input.Observacoes;
            existeModificacao = true;
        }

        AtualizarJustificativaComparacoes(input, justificativa, ref existeModificacao);

        if (existeModificacao)
        {
            await _repository.UpdateAsync(justificativa);

            var acaoJustificativaInput = await _acaoJustificativaAppService.GetAsync(acaoJustificativaId);

            if (codigoAcaoTipo == ECodigoAcaoTipo.CANCELAMENTO && acaoJustificativaInput.AcaoTipo.Codigo == ECodigoAcaoTipo.LIBERACAO)
            {
                //Excluir o espelho da requisição analisada e todas as requisições comparadas
                await _requisicaoDocumentoAppService.ExcluirEspelhoRequisicaoAsync(justificativa.NumeroProtocoloRequisicao);
            }
            else if (codigoAcaoTipo == ECodigoAcaoTipo.CANCELAMENTO && acaoJustificativaInput.AcaoTipo.Codigo == ECodigoAcaoTipo.CANCELAMENTO)
            {
                await _requisicaoDocumentoAppService.ExcluirEspelhoRequisicaoAsync(justificativa.NumeroProtocoloRequisicao);

                await _requisicaoDocumentoAppService.GerarEspelhoRequisicaoAsync(new CreateEspelhoRequisicaoDto()
                {
                    NumeroRequisicaoPrincipal = input.RequisicaoList!,
                    Observacao = input.Observacoes!,
                    Procedimento = input.Procedimento!,
                    RequisicoesComparada = input.RequisicoesComparadas!
                });
            }
            else if (codigoAcaoTipo == ECodigoAcaoTipo.LIBERACAO && acaoJustificativaInput.AcaoTipo.Codigo == ECodigoAcaoTipo.CANCELAMENTO)
            {
                await _requisicaoDocumentoAppService.GerarEspelhoRequisicaoAsync(new CreateEspelhoRequisicaoDto()
                {
                    NumeroRequisicaoPrincipal = input.RequisicaoList!,
                    Observacao = input.Observacoes!,
                    Procedimento = input.Procedimento!,
                    RequisicoesComparada = input.RequisicoesComparadas!
                });
            }
        }
    }

    public async Task<bool> ExcluirJustificativa(long requisicaoJustificativaId)
    {
        var justificativa = await _repository.GetAsync(x => x.RequisicaoJustificativaId == requisicaoJustificativaId);

        if (justificativa.JustificativaComparacao != null && justificativa.JustificativaComparacao.Count > 0)
            await _justificativaComparacaoManager.ExcluirMuitosAsync(justificativa.JustificativaComparacao);

        await _repository.DeleteAsync(justificativa);

        await _requisicaoDocumentoManager.ExcluirEspelhoRequisicaoAsync(justificativa.NumeroProtocoloRequisicao);

        return true;
    }

    #endregion

    private static void AtualizarJustificativaComparacoes(CreateUpdateJustificativaComplementoDto input, RequisicaoJustificativa justificativa, ref bool existeModificacao)
    {
        if (input.RequisicoesComparadas != null)
        {
            var comparacoesExistentes = justificativa.JustificativaComparacao?.ToList() ?? [];

            // Atualizar e adicionar
            foreach (var comparacaoInput in input.RequisicoesComparadas)
            {
                var comparacaoExistente = comparacoesExistentes
                    .FirstOrDefault(x => x.NumeroProtocoloRequisicao == comparacaoInput.NumeroRequisicao && x.RequisicaoJustificativaId == justificativa.RequisicaoJustificativaId);

                if (comparacaoExistente != null && !string.Equals(comparacaoExistente.Observacoes, comparacaoInput.Observacoes))
                {
                    comparacaoExistente.Observacoes = comparacaoInput.Observacoes;
                    existeModificacao = true;
                }
                else if (comparacaoExistente == null)
                {
                    justificativa.JustificativaComparacao!.Add(new JustificativaComparacao
                    {
                        NumeroProtocoloRequisicao = comparacaoInput.NumeroRequisicao!,
                        Observacoes = comparacaoInput.Observacoes,
                        RequisicaoJustificativaId = comparacaoInput.RequisicaoJustificativaId
                    });
                    existeModificacao = true;
                }
            }

            // Excluir
            var comparacoesParaExcluir = comparacoesExistentes
                .Where(x => !input.RequisicoesComparadas.Any(c => c.NumeroRequisicao == x.NumeroProtocoloRequisicao))
                .ToList();

            foreach (var comparacaoParaExcluir in comparacoesParaExcluir)
            {
                comparacaoParaExcluir.RequisicaoJustificativaId = justificativa.RequisicaoJustificativaId;
                justificativa.JustificativaComparacao!.Remove(comparacaoParaExcluir);
                existeModificacao = true;
            }
        }
    }

    #region Protected Methods

    protected override Task DeleteByIdAsync(long id)
    {
        return _manager.ExcluirAsync(e =>
            e.RequisicaoJustificativaId == id
        );
    }

    protected override async Task<RequisicaoJustificativa> GetEntityByIdAsync(long id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.RequisicaoJustificativaId == id
            ));
    }

    protected override IQueryable<RequisicaoJustificativa> ApplyDefaultSorting(IQueryable<RequisicaoJustificativa> query)
    {
        return query.OrderBy(e => e.RequisicaoJustificativaId);
    }

    protected override async Task<IQueryable<RequisicaoJustificativa>> CreateFilteredQueryAsync(RequisicaoJustificativaGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.TipoProcedimento.HasValue, x => x.RequisicaoProtocolo.TipoProcedimentoId == input.TipoProcedimento.ToString()!)
            .WhereIf(!input.NumeroRequisicao.IsNullOrWhiteSpace(), x => x.NumeroProtocoloRequisicao.Contains(input.NumeroRequisicao!))
            .WhereIf(input.AnaliseTelaId.HasValue, x => x.AnaliseTelaId == (int)input.AnaliseTelaId!)
            .WhereIf(!input.Usuario.IsNullOrWhiteSpace(), x => x.NomeUsuario.Contains(input.Usuario!))
            .WhereIf(input.DataInicio != null, x => x.DataAnalise >= input.DataInicio)
            .WhereIf(input.DataTermino != null, x => x.DataAnalise <= input.DataTermino)
            .WhereIf(input.Decisao.HasValue, x => x.AcaoJustificativa.AcaoTipoId == (int)input.Decisao!)
            .Include(x => x.RequisicaoProtocolo)
            .Include(x => x.AnaliseTela)
            .Include(x => x.AcaoJustificativa)
            .ThenInclude(x => x.AcaoTipo)
            .Include(x => x.JustificativaComparacao);
    }
    #endregion
}