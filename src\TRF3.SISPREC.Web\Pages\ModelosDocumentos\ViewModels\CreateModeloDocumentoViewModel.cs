using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.ModelosDocumentos.ViewModels;

public class CreateModeloDocumentoViewModel
{
    [Display(Name = "Nome Modelo")]
    [Required]
    public string? NomeModelo { get; set; }

    [Display(Name = "Setor")]
    [Required]
    [DynamicFormIgnore]
    public int? SetorId { get; set; }
    public List<SelectListItem> SetorLookupList { get; set; } = [];
    [HiddenInput]
    public string? TextoDocumento { get; set; }
}