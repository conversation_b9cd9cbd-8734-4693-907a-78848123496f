using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.ModelosDocumentos.ViewModels;

public class CreateModeloDocumentoViewModel : IValidatableObject
{
    [Display(Name = "Nome Modelo")]
    [Required(ErrorMessage = "O campo Nome Modelo é obrigatório.")]
    [StringLength(255, ErrorMessage = "O Nome Modelo deve ter no máximo 255 caracteres.")]
    public string? NomeModelo { get; set; }

    [Display(Name = "Setor")]
    [Required(ErrorMessage = "O campo Setor é obrigatório.")]
    [DynamicFormIgnore]
    public int? SetorId { get; set; }
    public List<SelectListItem> SetorLookupList { get; set; } = [];

    [HiddenInput]
    public string? TextoDocumento { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        // Validação do Nome Modelo
        if (string.IsNullOrWhiteSpace(NomeModelo))
        {
            yield return new ValidationResult("O campo Nome Modelo é obrigatório.", new[] { nameof(NomeModelo) });
        }

        // Validação do Setor
        if (!SetorId.HasValue || SetorId.Value <= 0)
        {
            yield return new ValidationResult("O campo Setor é obrigatório.", new[] { nameof(SetorId) });
        }

        // Validação do Texto do Documento
        if (string.IsNullOrWhiteSpace(TextoDocumento) || TextoDocumento.Equals("<p><br></p>", StringComparison.OrdinalIgnoreCase))
        {
            yield return new ValidationResult("O campo Texto do Documento é obrigatório e não pode ser vazio.", new[] { nameof(TextoDocumento) });
        }
    }
}