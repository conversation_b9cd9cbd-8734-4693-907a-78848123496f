using NSubstitute;
using Shouldly;
using System.Linq.Expressions;
using TRF3.SISPREC.BlocosSisprec;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.ExpedientesAdministrativos.Servicos;
using TRF3.SISPREC.SincronizacaoLegado;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace TRF3.SISPREC.ExpedientesAdministrativos
{
    public class ExpedienteAdministrativoManagerTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
    {

        #region privates

        private readonly ExpedienteAdministrativoManager _requisicaoExpedienteAdministrativoManager;
        private readonly IReqPagUnitOfWork _reqPagUnitOfWork;
        private readonly IExpedienteAdministrativoRepository _expedienteAdministrativoRepository;
        private readonly IBlocoSisprecRepository _blocoSisprecRepository;
        private readonly ICurrentUser _currentUser;

        #endregion

        public ExpedienteAdministrativoManagerTests()
        {
            _reqPagUnitOfWork = Substitute.For<IReqPagUnitOfWork>();
            _expedienteAdministrativoRepository = Substitute.For<IExpedienteAdministrativoRepository>();
            _blocoSisprecRepository = Substitute.For<IBlocoSisprecRepository>();
            _currentUser = Substitute.For<ICurrentUser>();


            _requisicaoExpedienteAdministrativoManager = new ExpedienteAdministrativoManager(
                                _expedienteAdministrativoRepository,
                                _reqPagUnitOfWork,
                                _blocoSisprecRepository,
                                _currentUser
                            );
        }

        [Fact]
        public async void IncluirExpedienteEmBlocoExistente_Deve_Inserir_Corretamente()
        {
            //Arrange
            var expedienteAdministrativo = inicializaExpedienteAdministrativo();
            _expedienteAdministrativoRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<ExpedienteAdministrativo, bool>>>()).Returns(expedienteAdministrativo);
            _blocoSisprecRepository.InsertAsync(Arg.Any<BlocoSisprec>(), true).Returns(new BlocoSisprec
            {
                BlocoSisprecId = 1,
                DataCriacao = DateTime.Now,
                IsDeleted = false,
                NomeUsuario = "Usuario Teste"
            });

            //Act
            await _requisicaoExpedienteAdministrativoManager.IncluirExpedienteEmBlocoExistenteAsync(expedienteAdministrativo.ExpedienteAdministrativoId, 1);

            //Assert
            await _expedienteAdministrativoRepository
                .Received(1)
                .UpdateAsync(Arg.Is<ExpedienteAdministrativo>(x => x.BlocoSisprecId == 1));
        }


        [Fact]
        public async void GerarBlocoAsync_Deve_Gerar_Corretamente()
        {
            // Arrange
            var expedienteAdministrativo = inicializaExpedienteAdministrativo();
            _currentUser.UserName.Returns("Usuario Teste");

            _blocoSisprecRepository.InsertAsync(Arg.Any<BlocoSisprec>(), true).Returns(new BlocoSisprec
            {
                BlocoSisprecId = 1,
                DataCriacao = DateTime.Now,
                IsDeleted = false,
                NomeUsuario = "Usuario Teste"
            });

            // Act
            await _requisicaoExpedienteAdministrativoManager.GerarBlocoAsync(expedienteAdministrativo);

            // Assert
            await _blocoSisprecRepository
                .Received(1)
                .InsertAsync(
                    Arg.Is<BlocoSisprec>(x => x.NomeUsuario == "Usuario Teste"),
                    true,
                    Arg.Any<CancellationToken>());

            await _expedienteAdministrativoRepository
                .Received(1)
                .UpdateAsync(Arg.Is<ExpedienteAdministrativo>(x => x.BlocoSisprecId == 1));

        }


        [Fact]
        public async void InserirImportacaoAsync_Deve_Retornar_Entidade_Existente()
        {
            // Arrange
            var expedienteAdministrativo = inicializaExpedienteAdministrativo();

            // Act  
            _expedienteAdministrativoRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<ExpedienteAdministrativo, bool>>>()).Returns(Task.FromResult(expedienteAdministrativo));

            var retultado = await _requisicaoExpedienteAdministrativoManager.InserirImportacaoAsync(1);

            // Assert
            retultado.ShouldNotBeNull();
            retultado.ShouldBe(expedienteAdministrativo);
        }

        [Fact]
        public async void InserirImportacaoAsync_Deve_Importar_Corretamente()
        {
            // Arrange
            var expedienteAdministrativoReqPag = inicializaExpedienteAdministrativoReqPag();

            // Act  

            _reqPagUnitOfWork.ExpedienteAdministrativoRepository.BuscaPorCodigoLista(Arg.Any<int>()).Returns(expedienteAdministrativoReqPag);
            _expedienteAdministrativoRepository
                .FirstOrDefaultAsync(Arg.Any<Expression<Func<ExpedienteAdministrativo, bool>>>())
                .Returns((ExpedienteAdministrativo)null);

            await _requisicaoExpedienteAdministrativoManager.InserirImportacaoAsync(1);

            // Assert
            await _expedienteAdministrativoRepository
            .Received(1)
            .InsertAsync(Arg.Is<ExpedienteAdministrativo>(x => x.ObservacaoExpedienteAdministrativo == "Teste Expediente"));

        }

        [Fact]
        public async void InserirAsync_Deve_Gerar_Uma_RequisicaoExpedienteAdministrativo_Corretamente()
        {
            // Arrange
            var expedienteAdministrativo = inicializaExpedienteAdministrativo();

            // Act         
            await _requisicaoExpedienteAdministrativoManager.InserirAsync(expedienteAdministrativo);

            // Assert
            await _expedienteAdministrativoRepository
            .Received(1)
            .InsertAsync(Arg.Is<ExpedienteAdministrativo>(c => c.NomeUsuario == "Usuario Teste"));
        }

        [Fact]
        public async void DetalhesRequisicaoAutorExpediente_Deve_Gerar_Varios_RequisicaoExpedienteAdministrativo_Corretamente()
        {
            // Arrange
            var expedienteAdministrativo = inicializaExpedienteAdministrativo();

            // Act    
            await _requisicaoExpedienteAdministrativoManager.InserirAsync(expedienteAdministrativo);
            await _requisicaoExpedienteAdministrativoManager.InserirAsync(expedienteAdministrativo);

            // Assert
            await _expedienteAdministrativoRepository
            .Received(2)
            .InsertAsync(Arg.Is<ExpedienteAdministrativo>(c => c.NomeUsuario == "Usuario Teste"));
        }

        #region CriaObjetos

        public List<SISPREC.SincronizacaoLegado.Models.ReqPag.ExpedienteAdministrativo> inicializaExpedienteAdministrativoReqPag()
        {
            return new List<SincronizacaoLegado.Models.ReqPag.ExpedienteAdministrativo> {
                new SISPREC.SincronizacaoLegado.Models.ReqPag.ExpedienteAdministrativo
                {
                    num_expedi_admini= 1,
                    dat_expedi_admini= DateTime.Now,
                    des_observ_expedi_admini = "Teste Expediente",
                    tip_expedi_admini = "O",
                    cod_usuari= "Usuario Teste"
                } ,
                new SISPREC.SincronizacaoLegado.Models.ReqPag.ExpedienteAdministrativo
                {
                    num_expedi_admini= 2,
                    dat_expedi_admini= DateTime.Now,
                    des_observ_expedi_admini = "Teste Expediente",
                    tip_expedi_admini = "C",
                    cod_usuari= "Usuario 2 Teste"
                }
            };
        }
        public ExpedienteAdministrativo inicializaExpedienteAdministrativo()
        {
            return new ExpedienteAdministrativo
            {
                NumeroExpedienteAdministrativo = 1,
                DataExpedienteAdministrativo = DateTime.Now,
                TipoExpedienteAdministrativo = ETipoExpedienteAdministrativo.OUTROS,
                NomeUsuario = "Usuario Teste"
            };
        }
        #endregion
    }
}
