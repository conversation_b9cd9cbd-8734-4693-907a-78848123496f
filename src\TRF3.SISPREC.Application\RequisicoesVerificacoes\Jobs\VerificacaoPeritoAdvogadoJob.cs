using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using TRF3.SISPREC.VerificacaoPeritosAdvogados;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoPeritoAdvogadoJob : AsyncBackgroundJob<VerificacaoPeritoAdvogadoJobArgs>, IVerificacaoPeritoAdvogadoJob
{
    private readonly IVerificacaoPeritoAdvogadoService _verificacaoPeritoAdvogadoService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public VerificacaoPeritoAdvogadoJob(IVerificacaoPeritoAdvogadoService verificacaoPeritoAdvogadoService, IUnitOfWorkManager unitOfWorkManager)
    {
        _verificacaoPeritoAdvogadoService = verificacaoPeritoAdvogadoService;
        _unitOfWorkManager = unitOfWorkManager;
    }

    [ExcludeFromCodeCoverage]
    public override async Task ExecuteAsync(VerificacaoPeritoAdvogadoJobArgs args)
    {
        try
        {
            if (args.RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar VerificacaoSuplementarIgualJob. RequisicaoVerificacaoId inválido: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
                return;
            }

            if (string.IsNullOrEmpty(args.NumeroProtocoloRequisicao))
            {
                Logger.LogError("Erro ao executar VerificacaoSuplementarIgualJob. NumeroProtocoloRequisicao inválido: NumeroProtocoloRequisicao: {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
                return;
            }

            using IUnitOfWork uow = _unitOfWorkManager.Begin(false, true);
            await _verificacaoPeritoAdvogadoService.VerificarPeritoAdvogadoAsync(args.RequisicaoVerificacaoId, args.NumeroProtocoloRequisicao);
            await uow.CompleteAsync();

        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoSuplementarIgualJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
            throw;
        }
    }
}

[ExcludeFromCodeCoverage]
public class VerificacaoPeritoAdvogadoJobArgs : BaseBackgroundJobArgs
{
    public long RequisicaoVerificacaoId { get; set; }
    public string NumeroProtocoloRequisicao { get; set; }

    public override string JobGroupName => VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoJobGroupName;

    public VerificacaoPeritoAdvogadoJobArgs(long requisicaoVerificacaoId, string numeroProtocoloRequisicao)
    {
        RequisicaoVerificacaoId = requisicaoVerificacaoId;
        NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
    }
}