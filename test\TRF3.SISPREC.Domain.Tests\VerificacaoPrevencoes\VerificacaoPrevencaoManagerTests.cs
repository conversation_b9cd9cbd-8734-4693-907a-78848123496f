using MockQueryable.NSubstitute;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using System.Linq.Expressions;
using TRF3.SISPREC.AcaoTipos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.OcorrenciaMotivos;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos;
using TRF3.SISPREC.RequisicoesVerificacoes;
using TRF3.SISPREC.VerificacaoPrevencoes.Servicos;
using TRF3.SISPREC.VerificacaoTipos;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.VerificacaoPrevencoes
{
    public class VerificacaoPrevencaoManagerTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
    {
        #region Read-Only Fields

        private readonly IVerificacaoPrevencaoRepository _repository;
        private readonly IRequisicaoVerificacaoRepository _verificacaoRepository;
        private readonly IVerificacaoTipoRepository _verificacaoTipoRepository;
        private readonly IRequisicaoOcorrenciaRepository _requisicaoOcorrenciaRepository;
        private readonly IAcaoTipoRepository _acaoTipoRepository;
        private readonly IOcorrenciaMotivoRepository _ocorrenciaMotivoRepository;
        private readonly IRequisicaoPropostaRepository _requisicaoPropostaRepository;
        private readonly IVerificacaoPrevencaoManager _verificacaoManager;

        public static TheoryData<EVerificacaoRequisicaoTipo> VerificacaoPrevencaoTipo
            => new()
            {
                EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_21,
                EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_22,
                EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_23,
                EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_24,
                EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_31,
                EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_32,
                EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_34,
                EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_35,
            };

        #endregion

        #region Constructors

        public VerificacaoPrevencaoManagerTests()
        {
            _repository = Substitute.For<IVerificacaoPrevencaoRepository>();
            _verificacaoRepository = Substitute.For<IRequisicaoVerificacaoRepository>();
            _verificacaoTipoRepository = Substitute.For<IVerificacaoTipoRepository>();
            _requisicaoOcorrenciaRepository = Substitute.For<IRequisicaoOcorrenciaRepository>();
            _acaoTipoRepository = Substitute.For<IAcaoTipoRepository>();
            _ocorrenciaMotivoRepository = Substitute.For<IOcorrenciaMotivoRepository>();
            _requisicaoPropostaRepository = Substitute.For<IRequisicaoPropostaRepository>();

            _verificacaoManager = new VerificacaoPrevencaoManager(_repository,
                                                                    _verificacaoRepository,
                                                                    _verificacaoTipoRepository,
                                                                    _requisicaoOcorrenciaRepository,
                                                                    _acaoTipoRepository,
                                                                    _ocorrenciaMotivoRepository,
                                                                    _requisicaoPropostaRepository);
        }

        #endregion

        #region Tests

        [Theory]
        [MemberData(nameof(VerificacaoPrevencaoTipo))]
        public async Task VerificarPrevencao_Deve_Gerar_Ocorrencia(EVerificacaoRequisicaoTipo verificacaoRequisicaoTipo)
        {
            //Arrange
            string numeroProtocolo = "00021";

            GerarMockRepository(verificacaoRequisicaoTipo, true);
            GerarMockVerificacaoRepository(verificacaoRequisicaoTipo);
            GerarMockVerificaTipoReposity();
            GerarMockOcorrenciaMotivoRepository(verificacaoRequisicaoTipo);
            GerarMockAcaoTipoRepository();
            GerarMockRequisicaoPropostaRepository();

            //Act
            await _verificacaoManager.ProcessarVerificacaoAsync(numeroProtocolo, verificacaoRequisicaoTipo);

            //Assert
            await _requisicaoOcorrenciaRepository.Received(1).InsertAsync(Arg.Any<RequisicaoOcorrencia>());
            await _requisicaoPropostaRepository.Received(1).UpdateAsync(Arg.Any<RequisicaoProposta>());
            await _verificacaoRepository.Received(1).UpdateAsync(Arg.Any<RequisicaoVerificacao>());
        }

        [Theory]
        [MemberData(nameof(VerificacaoPrevencaoTipo))]
        public async Task VerificarPrevencaoTipo21_Nao_Deve_Gerar_Ocorrencia(EVerificacaoRequisicaoTipo verificacaoRequisicaoTipo)
        {
            //Arrange
            string numeroProtocolo = "00021";

            GerarMockRepository(verificacaoRequisicaoTipo, false);
            GerarMockVerificacaoRepository(verificacaoRequisicaoTipo);
            GerarMockAcaoTipoRepository();
            GerarMockOcorrenciaMotivoRepository(verificacaoRequisicaoTipo);
            GerarMockVerificaTipoReposity();
            GerarMockRequisicaoPropostaRepository();

            //Act
            await _verificacaoManager.ProcessarVerificacaoAsync(numeroProtocolo, verificacaoRequisicaoTipo);

            //Assert
            await _requisicaoOcorrenciaRepository.Received(0).InsertAsync(Arg.Any<RequisicaoOcorrencia>());
            await _requisicaoPropostaRepository.Received(0).UpdateAsync(Arg.Any<RequisicaoProposta>());
            await _verificacaoRepository.Received(1).UpdateAsync(Arg.Any<RequisicaoVerificacao>());
        }

        #endregion

        #region Private

        private void GerarMockRepository(EVerificacaoRequisicaoTipo verificacaoRequisicaoTipo, bool gerarOcorrencia)
        {
            var parametrosPrevencao = new ParametrosPrevencaoDto()
            {
                NumeroProcessoOriginario = "000001",
                NumeroCnpjCpfRequerente = "12345678909",
                AssuntoId = 1,
                UnidadeId = 2,
                UnidadeJudicialId = 3,
                NomeParteAutora = "Nome parte autora teste"
            };

            var listaParametrosPrevencao = new List<ParametrosPrevencaoDto> { parametrosPrevencao }
                .AsQueryable()
                .BuildMockDbSet();

            switch (verificacaoRequisicaoTipo)
            {
                case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_21:
                    _repository
                        .BuscarParametrosPrevencaoTipo21Async(Arg.Any<string>())
                        .Returns(listaParametrosPrevencao);
                    _repository
                        .BuscarRequisicoesPrevencaoTipo21Async(Arg.Any<ParametrosPrevencaoDto>(), Arg.Any<string>())
                        .Returns(gerarOcorrencia ? ["000021"] : []);
                    break;
                case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_22:
                    _repository
                        .BuscarParametrosPrevencaoTipo22Async(Arg.Any<string>())
                        .Returns(listaParametrosPrevencao);
                    _repository
                        .BuscarRequisicoesPrevencaoTipo22Async(Arg.Any<ParametrosPrevencaoDto>(), Arg.Any<string>())
                        .Returns(gerarOcorrencia ? ["000022"] : []);
                    break;
                case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_23:
                    _repository
                        .BuscarParametrosPrevencaoTipo23Async(Arg.Any<string>())
                        .Returns(listaParametrosPrevencao);
                    _repository
                        .BuscarRequisicoesPrevencaoTipo23Async(Arg.Any<ParametrosPrevencaoDto>(), Arg.Any<string>())
                        .Returns(gerarOcorrencia ? ["000023"] : []);
                    break;
                case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_24:
                    _repository
                        .BuscarParametrosPrevencaoTipo24Async(Arg.Any<string>())
                        .Returns(listaParametrosPrevencao);
                    _repository
                        .BuscarRequisicoesPrevencaoTipo24Async(Arg.Any<ParametrosPrevencaoDto>(), Arg.Any<string>())
                        .Returns(gerarOcorrencia ? ["000024"] : []);
                    break;
                case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_31:
                    _repository
                        .BuscarParametrosPrevencaoTipo31Async(Arg.Any<string>())
                        .Returns(listaParametrosPrevencao);
                    _repository
                        .BuscarRequisicoesPrevencaoTipo31Async(Arg.Any<ParametrosPrevencaoDto>(), Arg.Any<string>())
                        .Returns(gerarOcorrencia ? ["000031"] : []);
                    break;
                case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_32:
                    _repository
                        .BuscarParametrosPrevencaoTipo32Async(Arg.Any<string>())
                        .Returns(listaParametrosPrevencao);
                    _repository
                        .BuscarRequisicoesPrevencaoTipo32Async(Arg.Any<ParametrosPrevencaoDto>(), Arg.Any<string>())
                        .Returns(gerarOcorrencia ? ["000032"] : []);
                    break;
                case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_34:
                    _repository
                        .BuscarParametrosPrevencaoTipo34Async(Arg.Any<string>())
                        .Returns(listaParametrosPrevencao);
                    _repository
                        .BuscarRequisicoesPrevencaoTipo34Async(Arg.Any<ParametrosPrevencaoDto>(), Arg.Any<string>())
                        .Returns(gerarOcorrencia ? ["000034"] : []);
                    break;
                case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_35:
                    _repository
                        .BuscarParametrosPrevencaoTipo35Async(Arg.Any<string>())
                        .Returns(listaParametrosPrevencao);
                    _repository
                        .BuscarRequisicoesPrevencaoTipo35Async(Arg.Any<ParametrosPrevencaoDto>(), Arg.Any<string>())
                        .Returns(gerarOcorrencia ? ["000035"] : []);
                    break;
                default:
                    throw new ArgumentException($"Tipo de verificação de requisição não tratado {verificacaoRequisicaoTipo}.");
            }

            _repository
                .ConsultaRequisicaoReincluidoComEstornoAsync(Arg.Any<string>(), Arg.Any<string>())
                .ReturnsNull();
        }

        private void GerarMockVerificaTipoReposity()
        {
            var verificacaoTipo = new VerificacaoTipo()
            {
                Ativo = true,
                DescricaoTipo = "Verificação de Prevenção",
                VerificacaoTipoId = 1,
            };

            var verificacaoTipoList = new List<VerificacaoTipo> { verificacaoTipo }
                .AsQueryable()
                .BuildMockDbSet();

            _verificacaoTipoRepository
                .GetQueryableAsync()
                .Returns(verificacaoTipoList);

            _verificacaoTipoRepository
                .FirstOrDefaultAsync(Arg.Any<Expression<Func<VerificacaoTipo, bool>>>())
                .Returns(verificacaoTipo);
        }

        private void GerarMockAcaoTipoRepository()
        {
            var verificacaoTipo = new AcaoTipo()
            {
                AcaoTipoId = 1,
                Ativo = true,
                Codigo = Enums.ECodigoAcaoTipo.PENDENTE,
                Descricao = Enums.EDescricaoAcaoTipo.PENDENTE,
            };

            var acaotipoList = new List<AcaoTipo> { verificacaoTipo }
                .AsQueryable()
                .BuildMockDbSet();

            _acaoTipoRepository
                .GetQueryableAsync()
                .Returns(acaotipoList);

            _acaoTipoRepository
                .FirstOrDefaultAsync(Arg.Any<Expression<Func<AcaoTipo, bool>>>())
                .Returns(verificacaoTipo);
        }

        private void GerarMockOcorrenciaMotivoRepository(EVerificacaoRequisicaoTipo verificacaoRequisicaoTipo)
        {
            var ocorrenciaMotivo = new OcorrenciaMotivo()
            {
                CodigoMotivo = ObterCodigoMotivoPorTipoPrevencao(verificacaoRequisicaoTipo),
                Ativo = true,
                DescricaoMotivo = "Apenas um teste",
                OcorrenciaMotivoId = 1,
            };

            var ocorrenciaMotivoList = new List<OcorrenciaMotivo> { ocorrenciaMotivo }
                .AsQueryable()
                .BuildMockDbSet();

            _ocorrenciaMotivoRepository
                .GetQueryableAsync()
                .Returns(ocorrenciaMotivoList);

            _ocorrenciaMotivoRepository
                .FirstOrDefaultAsync(Arg.Any<Expression<Func<OcorrenciaMotivo, bool>>>())
                .Returns(ocorrenciaMotivo);

            #region Método auxiliar

            static int ObterCodigoMotivoPorTipoPrevencao(EVerificacaoRequisicaoTipo verificacaoRequisicaoTipo)
                => verificacaoRequisicaoTipo switch
                {
                    EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_21 => OcorrenciaMotivoConsts.CODIGO_MOTIVO_PREVENCAO_TIPO_21,
                    EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_22 => OcorrenciaMotivoConsts.CODIGO_MOTIVO_PREVENCAO_TIPO_22,
                    EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_23 => OcorrenciaMotivoConsts.CODIGO_MOTIVO_PREVENCAO_TIPO_23,
                    EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_24 => OcorrenciaMotivoConsts.CODIGO_MOTIVO_PREVENCAO_TIPO_24,
                    EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_31 => OcorrenciaMotivoConsts.CODIGO_MOTIVO_PREVENCAO_TIPO_31,
                    EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_32 => OcorrenciaMotivoConsts.CODIGO_MOTIVO_PREVENCAO_TIPO_32,
                    EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_34 => OcorrenciaMotivoConsts.CODIGO_MOTIVO_PREVENCAO_TIPO_34,
                    EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_35 => OcorrenciaMotivoConsts.CODIGO_MOTIVO_PREVENCAO_TIPO_35,
                    _ => 0,
                };

            #endregion
        }

        private void GerarMockRequisicaoPropostaRepository()
        {
            var requisicaoProposta = new RequisicaoProposta()
            {
                PropostaId = 1,
                NumeroProtocoloRequisicao = "00021",
                SituacaoRequisicaoProposta = Enums.ESituacaoRequisicaoProposta.PENDENTE,
                RequisicaoProtocolo = new RequisicaoProtocolo()
                {
                    SituacaoRequisicaoId = 1
                }
            };

            var requisicaoPropostaList = new List<RequisicaoProposta> { requisicaoProposta }
                .AsQueryable()
                .BuildMockDbSet();

            _requisicaoPropostaRepository
                .GetQueryableAsync()
                .Returns(requisicaoPropostaList);

            _requisicaoPropostaRepository
                .FirstOrDefaultAsync(Arg.Any<Expression<Func<RequisicaoProposta, bool>>>())
                .Returns(requisicaoProposta);
        }

        private void GerarMockVerificacaoRepository(EVerificacaoRequisicaoTipo verificacaoRequisicaoTipo)
        {
            var requisicaoVerificacao = new RequisicaoVerificacao()
            {
                DataRequisicao = DateTime.Now,
                Executado = false,
                IsDeleted = false,
                NumeroProtocoloRequisicaoId = "00021",
                RequisicaoVerificacaoId = 1,
                VerificacaoTipoId = (int)verificacaoRequisicaoTipo
            };

            var requisicaoVerificacaoList = new List<RequisicaoVerificacao> { requisicaoVerificacao }
                .AsQueryable()
                .BuildMockDbSet();

            _verificacaoRepository
                .GetQueryableAsync()
                .Returns(requisicaoVerificacaoList);

            _verificacaoRepository
                .FirstOrDefaultAsync(Arg.Any<Expression<Func<RequisicaoVerificacao, bool>>>())
                .Returns(requisicaoVerificacao);
        }

        #endregion
    }
}
