using Microsoft.EntityFrameworkCore;
using System.Linq.Dynamic.Core;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.ExpedientesAdministrativos;
using TRF3.SISPREC.ExpedientesAdministrativos.Dtos;
using Volo.Abp;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.ExpedientesAdministrativo
{
    public class ExpedienteAdministrativoAppService : BaseAppService, IExpedienteAdministrativoAppService
    {
        #region Read-Only Fields

        private readonly IExpedienteAdministrativoRepository _repository;
        private readonly IExpedienteAdministrativoManager _manager;

        #endregion

        #region Constructors

        public ExpedienteAdministrativoAppService(IExpedienteAdministrativoRepository repository, IExpedienteAdministrativoManager manager)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _manager = manager ?? throw new ArgumentNullException(nameof(manager));
        }

        #endregion

        #region IExpedienteAdministrativoAppService Members

        public async Task<PagedResultDto<ExpedienteAdministrativoDto>> GetExpedientes(ExpedienteAdministrativoGetListInput input)
        {
            var query = (await _repository.GetQueryableAsync())
                            .Include(x => x.RequisicaoExpedienteAdministrativo)
                            .WhereIf(input.NumeroProcessoSei != null, x => x.NumeroProcessoSei == input.NumeroProcessoSei)
                            .WhereIf(input.TipoExpedienteAdministrativo != null, x => x.TipoExpedienteAdministrativo == input.TipoExpedienteAdministrativo)
                            .WhereIf(!input.NomeUsuario.IsNullOrWhiteSpace(), x => x.NomeUsuario == input.NomeUsuario)
                            .WhereIf(input.StatusExpedienteAdminstrativo != null, x => x.StatusExpedienteAdminstrativo == input.StatusExpedienteAdminstrativo)
                            .WhereIf(input.BlocoSisprecId != null, x => x.BlocoSisprecId == input.BlocoSisprecId)
                            .WhereIf(!input.NumeroRequisicao.IsNullOrWhiteSpace(), x => x.RequisicaoExpedienteAdministrativo.Any(r => r.NumeroProtocoloRequisicao == input.NumeroRequisicao))
                            .Select(x => new ExpedienteAdministrativoDto()
                            {
                                NumeroProcessoSei = x.NumeroProcessoSei,
                                TipoExpedienteAdministrativo = x.TipoExpedienteAdministrativo.GetEnumDescription(),
                                DataExpedienteAdministrativo = x.DataExpedienteAdministrativo,
                                NomeUsuario = x.NomeUsuario,
                                ObservacaoExpedienteAdministrativo = x.ObservacaoExpedienteAdministrativo,
                                StatusExpedienteAdminstrativo = x.StatusExpedienteAdminstrativo.GetEnumDescription(),
                                BlocoSisprecId = x.BlocoSisprecId,
                                ExpedienteAdministrativoId = x.ExpedienteAdministrativoId
                            })
                            .Distinct()
                            .OrderByDescending(x => x.DataExpedienteAdministrativo)
                            .ThenByDescending(x => x.NumeroProcessoSei)
                            .AsQueryable();

            var totalCount = await query.CountAsync();
            query = query.OrderByIf<ExpedienteAdministrativoDto, IQueryable<ExpedienteAdministrativoDto>>(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting).PageBy(input);
            return new PagedResultDto<ExpedienteAdministrativoDto>
            {
                Items = query.ToList(),
                TotalCount = totalCount
            };
        }

        public async Task IncluirExpedienteEmBlocoExistente(CreateSemBlocoDto blocoDto)
        {
            foreach (var expedienteId in blocoDto.ExpedienteId)
                await _manager.IncluirExpedienteEmBlocoExistenteAsync(expedienteId, blocoDto.BlocoId);
        }

        public async Task GerarBloco(List<SemBlocosDto> blocosDto)
        {
            var tiposUnicos = blocosDto
                .Select(x => x.Tipo.ToLower())
                .Distinct()
                .ToList();

            if (tiposUnicos.Count > 1)
                throw new UserFriendlyException("Para geração do bloco, todos os expedientes devem ser do mesmo Tipo.");

            foreach (var bloco in blocosDto)
            {
                var expediente = await _repository.GetAsync(x => x.ExpedienteAdministrativoId == bloco.Id);
                await _manager.GerarBlocoAsync(expediente);
            }
        }

        public async Task<PagedResultDto<ExpedienteAdministrativoSemBlocoDto>> GetExpedientesSemBloco(ExpedienteAdministrativoSemBlocoGetListInput input)
        {
            #region Query Expediente Administrativo

            var query = (await _repository.GetQueryableAsync())
                    .Include(x => x.RequisicaoExpedienteAdministrativo)
                .WhereIf(input.NumeroProcessoSei != null, x => x.NumeroProcessoSei == input.NumeroProcessoSei)
                .WhereIf(input.TipoExpedienteAdministrativo != null, x => x.TipoExpedienteAdministrativo == input.TipoExpedienteAdministrativo)
                .WhereIf(!input.NomeUsuario.IsNullOrWhiteSpace(), x => x.NomeUsuario == input.NomeUsuario)
                .WhereIf(!input.NumeroRequisicao.IsNullOrWhiteSpace(), x => x.RequisicaoExpedienteAdministrativo.Any(r => r.NumeroProtocoloRequisicao == input.NumeroRequisicao))
                .Where(x => x.BlocoSisprecId == null && x.NumeroExpedienteAdministrativo == null)
                .Select(g => new ExpedienteAdministrativoSemBlocoDto
                {
                    NumeroProcessoSei = g.NumeroProcessoSei,
                    TipoExpedienteAdministrativo = g.TipoExpedienteAdministrativo.GetEnumDescription(),
                    DataExpedienteAdministrativo = g.DataExpedienteAdministrativo,
                    NomeUsuario = g.NomeUsuario,
                    ObservacaoExpedienteAdministrativo = g.ObservacaoExpedienteAdministrativo,
                    StatusExpedienteAdminstrativo = g.StatusExpedienteAdminstrativo.GetEnumDescription(),
                    BlocoSisprecId = g.BlocoSisprecId,
                    ExpedienteAdministrativoId = g.ExpedienteAdministrativoId
                })
                .Distinct()
                .OrderByDescending(x => x.DataExpedienteAdministrativo)
                .ThenByDescending(x => x.NumeroProcessoSei)
                .AsQueryable();

            #endregion

            var totalCount = await query.CountAsync();
            query = query.OrderByIf<ExpedienteAdministrativoSemBlocoDto, IQueryable<ExpedienteAdministrativoSemBlocoDto>>(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting).PageBy(input);
            return new PagedResultDto<ExpedienteAdministrativoSemBlocoDto>
            {
                Items = query.ToList(),
                TotalCount = totalCount
            };
        }

        #endregion
    }
}
