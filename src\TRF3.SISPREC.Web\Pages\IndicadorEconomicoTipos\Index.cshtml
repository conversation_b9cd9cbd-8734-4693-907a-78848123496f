@page
@using TRF3.SISPREC.Permissoes
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.TipoIndicadorEconomicos
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@inject IAuthorizationService Authorization
@{
    PageLayout.Content.Title = "Indicador Econômico - Tipo";
    PageLayout.Content.BreadCrumb.Add("TipoIndicadorEconomicos");
    PageLayout.Content.MenuItemName = SISPRECMenus.IndicadorEconomicoTipo;
}

@section scripts
{
    <abp-script src="/Pages/IndicadorEconomicoTipos/index.js" />
}

<abp-card>
    <abp-card-header>
        <abp-row class="justify-content-between align-items-center">
            <abp-column>
                <a abp-collapse-id="IndicadorEconomicoTipoCollapse" class="text-secondary">Filtrar</a>
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="NewIndicadorEconomicoTipoButton"
                            text="Novo"
                            icon="plus"
                            button-type="Primary" />
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <abp-dynamic-form abp-model="IndicadorEconomicoTipoFilter" id="IndicadorEconomicoTipoFilter" required-symbols="false" column-size="_3">
            <abp-collapse-body id="IndicadorEconomicoTipoCollapse">
                <abp-form-content />
            </abp-collapse-body>
        </abp-dynamic-form>
        <abp-table striped-rows="true" id="IndicadorEconomicoTipoTable" class="nowrap"/>
    </abp-card-body>
</abp-card>
