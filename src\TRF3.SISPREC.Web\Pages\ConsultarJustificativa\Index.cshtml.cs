using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicaoJustificativas.Dtos;
using TRF3.SISPREC.Web.Pages.ConsultarJustificativa.ViewModels;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.ConsultarJustificativa
{
    [ExcludeFromCodeCoverage] //Excluido da cobertura pois essa página ocorre erro de configuração do minio nos testes unitários
    public class IndexModel(IRequisicaoJustificativaAppService requisicaoJustificativaAppService) : SISPRECPageModel
    {
        public ConsultaJustificativaFilterInput ConsultaFilter { get; set; } = new();

        [BindProperty]
        public CreateUpdateRequisicaoJustificativaViewModel ViewModel { get; set; } = new();

        public virtual async Task OnGetAsync()
        {
            ConsultaFilter.AnaliseTelaLookupList.AddRange([.. EnumExtensions.GetEnumSelectList<EDescricaoAnaliseTela>().OrderBy(x => x.Text)]);
            await Task.CompletedTask;
        }

        public virtual async Task<IActionResult> OnPostAsync()
        {
            var obj = ObjectMapper.Map<CreateUpdateRequisicaoJustificativaViewModel, CreateUpdateJustificativaComplementoDto>(ViewModel);

            await requisicaoJustificativaAppService.EditarJustificativa(obj);

            return NoContent();
        }

        public class ConsultaJustificativaFilterInput
        {
            [FormControlSize(AbpFormControlSize.Small)]
            [Display(Name = "Procedimento")]
            public ETipoProcedimentoRequisicao? TipoProcedimento { get; set; } = null;

            [FormControlSize(AbpFormControlSize.Small)]
            [Display(Name = "Requisição")]
            [DynamicFormIgnore]
            public string? NumeroRequisicao { get; set; } = null;

            [FormControlSize(AbpFormControlSize.Small)]
            [Display(Name = "Tipo Análise")]
            [SelectItems(nameof(AnaliseTelaLookupList))]
            public int? AnaliseTelaId { get; set; } = null;
            public List<SelectListItem> AnaliseTelaLookupList { get; set; } =
            [
                new(string.Empty, null)
            ];

            [FormControlSize(AbpFormControlSize.Small)]
            [Display(Name = "Usuário")]
            public string? Usuario { get; set; } = null;

            [FormControlSize(AbpFormControlSize.Small)]
            [Display(Name = "Data Início")]
            public DateTime? DataInicio { get; set; } = null;

            [FormControlSize(AbpFormControlSize.Small)]
            [Display(Name = "Data Término")]
            public DateTime? DataTermino { get; set; } = null;

            [FormControlSize(AbpFormControlSize.Small)]
            [Display(Name = "Decisão")]
            public EDescricaoAcaoTipo? Decisao { get; set; } = null;
        }
    }
}
