using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BlocosSisprec;
using TRF3.SISPREC.ExpedientesAdministrativos;
using TRF3.SISPREC.ExpedientesAdministrativos.Dtos;
using TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos.ViewModels;

namespace TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos
{
    public class CreateModalModel : SISPRECPageModel
    {
        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public string? Tipo { get; set; }

        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public string? Id { get; set; }

        [BindProperty(SupportsGet = true)]
        public CreateViewModel ViewModel { get; set; } = new();

        public List<SelectListItem>? BlocoLista { get; set; }

        private readonly IBlocoSisprecAppService _blocoSisprecAppService;
        private readonly IExpedienteAdministrativoAppService _expedienteAdministrativo;

        public CreateModalModel(
                IBlocoSisprecAppService blocoSisprecAppService,
                IExpedienteAdministrativoAppService expedienteAdministrativoSemBlocoAppService
            )
        {
            _blocoSisprecAppService = blocoSisprecAppService ?? throw new ArgumentNullException(nameof(blocoSisprecAppService));
            _expedienteAdministrativo = expedienteAdministrativoSemBlocoAppService ?? throw new ArgumentNullException(nameof(expedienteAdministrativoSemBlocoAppService));
        }

        public async virtual Task OnGet()
        {
            var blocos = await _blocoSisprecAppService.GetBlocosGerados(Tipo);
            BlocoLista = blocos.Select(b => new SelectListItem
            {
                Value = b.BlocoSisprecId.ToString(),
                Text = b.BlocoSisprecId.ToString()
            }).ToList();
            ViewModel.ExpedienteId = Id.Split(',').Select(int.Parse).ToList();
        }

        [ExcludeFromCodeCoverage]
        public virtual async Task<IActionResult> OnPostAsync()
        {
            await _expedienteAdministrativo.IncluirExpedienteEmBlocoExistente(new CreateSemBlocoDto()
            {
                BlocoId = ViewModel.BlocoId,
                ExpedienteId = ViewModel.ExpedienteId
            });
            return NoContent();
        }
    }
}
