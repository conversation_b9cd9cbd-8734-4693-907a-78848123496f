abp.modals.cadastroJustificativa = function () {
    let listaExportacaoObservacao = window.listaExportacaoObservacao;
    $(document).ready(function () {
        let itemIdCounter = 0;
        const selectedItems = new Set();

        function toggleSelection(row) {
            const itemId = $(row).data('id');
            const isSelected = $(row).hasClass('selected');

            $(row).toggleClass('selected');
            isSelected ? selectedItems.delete(itemId) : selectedItems.add(itemId);
            updateDeleteButtonState();
        }

        function updateDeleteButtonState() {
            $('#deleteButton').prop('disabled', selectedItems.size === 0);
        }

        function deleteSelectedItems() {
            selectedItems.forEach(id => $(`.item[data-id="${id}"]`).remove());
            selectedItems.clear();
            updateDeleteButtonState();
            atualizarCampoOculto();
        }
        function verificaImportacao() {
            
            if (typeof listaExportacaoObservacao !== 'undefined' && listaExportacaoObservacao && listaExportacaoObservacao.size > 0) {
                importarObservacao(listaExportacaoObservacao);
            }
        }

        function importarObservacao(listaImportacao) {
            listaImportacao.forEach((value, key) => {
                $('#requisicoes-comparadas-table').append( criarNovaLinha(key, value));
            });
        }

        function criarNovaLinha(newItemText, newItemTextObservacao) {
            return `
                <tr class="item" data-id="${itemIdCounter++}">
                    <td class="numero-requisicao col-3">${newItemText}</td>
                    <td class="observacao">${newItemTextObservacao}</td>
                </tr>
            `;
        }

        function limparCamposEntrada() {
            $('#newItemText, #newItemTextObservacao').val('');
        }

        function addNewItem() {
            const newItemText = $('#newItemText').val().trim();
            const newItemTextObservacao = $('#newItemTextObservacao').val().trim();

            if (!newItemText) {
                abp.message.warn('Por favor, inserir um número de requisição para o novo item.');
                return;
            }

            if (newItemText.length > 20) {
                abp.message.warn('O nº de requisição deve ter no máximo 20 caracteres.');
                return;
            }

            if (!validarInclusaoRequisicao(newItemText)) {
                return;
            }

            const service = tRF3.sISPREC.requisicaoJustificativas.requisicaoJustificativa;
            verificarExistenciaRequisicao(newItemText, service);

            $('#requisicoes-comparadas-table').append(criarNovaLinha(newItemText, newItemTextObservacao));
            atualizarCampoOculto();
            limparCamposEntrada();
        }

        function atualizarCampoOculto() {
            const container = $('#requisicoesComparadas').empty();

            $('#requisicoes-comparadas-table tr').each((index, row) => {
                const numeroRequisicao = $(row).find('.numero-requisicao').text().trim();
                const observacoes = $(row).find('.observacao').text().trim();

                container.append(
                    $('<input>').attr({ type: 'hidden', name: `ViewModel.RequisicoesComparadas[${index}].NumeroRequisicao`, value: numeroRequisicao }),
                    $('<input>').attr({ type: 'hidden', name: `ViewModel.RequisicoesComparadas[${index}].Observacoes`, value: observacoes })
                );
            });
        }

        function configurarEventosTecla() {
            $('.newItemInput').on('keypress', function (e) {
                if (e.which === 13) {
                    e.preventDefault();
                    addNewItem();
                }
            });
        }

        function configurarEventosClique() {
            $('#requisicoes-comparadas-table').on('click', 'tr', function () {
                toggleSelection(this);
            });
            $('#deleteButton').on('click', deleteSelectedItems);
            $('#addButton').on('click', addNewItem);
        }

        // Configurar todos os eventos
        configurarEventosClique();
        configurarEventosTecla();
        verificaImportacao();
        verificaConfiguracaoDefault();
    });

    function initModal(modalManager, args) {
        const service = tRF3.sISPREC.acoesJustificativa.acaoJustificativa;
        const $form = modalManager.getForm();

        $form.find('#ViewModel_Decisao').on('change', function () {
            const decisaoValue = $(this).val();
            carregarMotivos(decisaoValue, service);
        });
    }

    return { initModal };
};
