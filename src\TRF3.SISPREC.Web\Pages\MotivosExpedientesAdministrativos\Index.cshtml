@page
@using TRF3.SISPREC.Permissoes
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.OcorrenciaMotivos
@using TRF3.SISPREC.Web.Menus
@model TRF3.SISPREC.Web.Pages.MotivosExpedientesAdministrativos.IndexModel
@inject IPageLayout PageLayout
@inject IAuthorizationService Authorization
@{
    PageLayout.Content.Title = "Motivo de Expediente Administrativo";
    PageLayout.Content.BreadCrumb.Add("MotivoExpedienteAdministrativo");
    PageLayout.Content.MenuItemName = SISPRECMenus.MotivoExpedienteAdministrativo;
}

@section scripts
{
    <abp-script src="/Pages/MotivosExpedientesAdministrativos/index.js" />
}
@section styles
{
    <abp-style src="/Pages/MotivosExpedientesAdministrativos/index.css" />
}

<abp-card>
    <abp-card-header>
        <abp-row class="justify-content-between align-items-center">
            <abp-column>
                <a abp-collapse-id="MotivoExpedienteAdministrativoCollapse" class="text-secondary">Filtrar</a>
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="NewMotivoExpedienteAdministrativoButton"
                            text="Novo"
                            icon="plus"
                            button-type="Primary" />
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <abp-dynamic-form abp-model="MotivoExpedienteAdministrativoFilter" id="MotivoExpedienteAdministrativoFilter" required-symbols="false" column-size="_3">
            <abp-collapse-body id="MotivoExpedienteAdministrativoCollapse">
                <abp-form-content />
            </abp-collapse-body>
        </abp-dynamic-form>
        <abp-table striped-rows="true" id="MotivoExpedienteAdministrativoTable" class="nowrap" />
    </abp-card-body>
</abp-card>
