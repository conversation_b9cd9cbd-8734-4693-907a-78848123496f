$(function () {
    // Configuração do Summernote
    $('#editor').summernote({
        placeholder: 'Digite seu texto aqui...',
        tabsize: 2,
        height: 250,
        toolbar: [
            ['style', ['style', 'bold', 'italic', 'underline', 'strikethrough', 'clear']],
            ['font', ['fontname', 'fontsize']],
            ['script', ['superscript', 'subscript']],
            ['color', ['color']],
            ['height', ['height']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link', 'table']],
            ['view', ['fullscreen', 'codeview']],
        ],
        lang: 'pt-BR',
    });

    // Torna a função saveModeloDocumento global
    window.saveModeloDocumento = function (successMessage) {
        $("#btnSalvar").on('click', function (event) {
            event.preventDefault();

            // Validação client-side básica (complementar à server-side)
            if (!validaCamposBasicos()) {
                return;
            }

            // Obtém o JS Client Proxy do ModeloDocumentoAppService (Dynamic Auto API)
            const modeloAppService = tRF3.sISPREC.modelosDocumentos.modeloDocumento;

            // DTO usado pelo client proxy
            let modeloDocumentoDto = {};

            // Preenche o DTO por meio dos campos do formulário
            $("#ModeloDocumentoForm").serializeArray().forEach(function (data) {
                if (data.value !== '') {
                    // Obtém o nome correto esperado pelo AppService
                    const fieldName = data.name.split('.').pop();
                    modeloDocumentoDto[fieldName] = data.value;
                }
            });

            // Atribui a propriedade do conteúdo do documento
            modeloDocumentoDto.TextoDocumento = $('#editor').summernote('code');

            // Verifica se a ação será de criação ou atualização
            let acao = modeloDocumentoDto.ModeloDocumentoId
                ? modeloAppService.update(modeloDocumentoDto.ModeloDocumentoId, modeloDocumentoDto)
                : modeloAppService.create(modeloDocumentoDto);

            acao
                .then(() => {
                    abp.notify.success(successMessage, "Sucesso", { okText: "OK" });
                    setTimeout(() => {
                        window.location.href = abp.appPath + 'ModelosDocumentos';
                    }, 500);
                })
                .catch((error) => {
                    // Tratamento de erros do ABP
                    if (error && error.details) {
                        abp.notify.error(error.details, "Erro de Validação");
                    } else {
                        abp.notify.error("Erro ao salvar o modelo de documento.", "Erro");
                    }
                });
        });
    }

    // Validação client-side básica (complementar à server-side)
    function validaCamposBasicos() {
        let isValid = true;

        // Limpa erros anteriores
        $('.field-validation-error').removeClass('field-validation-error').html('');
        $('.input-validation-error').removeClass('input-validation-error').attr('aria-invalid', 'false');

        // Valida Nome Modelo
        const $nomeModelo = $('#ViewModel_NomeModelo');
        if (!$nomeModelo.val().trim()) {
            mostrarErroValidacao($nomeModelo, 'O campo Nome Modelo é obrigatório.');
            isValid = false;
        }

        // Valida Setor
        const $setorId = $('#ViewModel_SetorId');
        if (!$setorId.val() || $setorId.val() === '') {
            mostrarErroValidacao($setorId, 'O campo Setor é obrigatório.');
            isValid = false;
        }

        // Valida Texto do Documento
        const textoDocumento = $('#editor').summernote('code');
        if (!textoDocumento || textoDocumento.trim() === '' || textoDocumento === '<p><br></p>') {
            mostrarErroValidacao($('#ViewModel_TextoDocumento'), 'O campo Texto do Documento é obrigatório.');
            isValid = false;
        }

        return isValid;
    }

    // Função auxiliar para mostrar erro de validação
    function mostrarErroValidacao($field, mensagem) {
        $field.addClass('input-validation-error').attr('aria-invalid', 'true');
        const errorSpan = $(`span[data-valmsg-for="${$field.attr('name')}"]`);
        if (errorSpan.length > 0) {
            errorSpan.addClass('field-validation-error');
            errorSpan.html('<span>' + mensagem + '</span>');
        }
    }

    document.getElementById("btnCancelar").addEventListener("click", function (event) {
        event.preventDefault();
        window.history.back(); // Retorna para a página anterior no histórico do navegador
    });

});