$(function () {
    // Configuração do Summernote
    $('#editor').summernote({
        placeholder: 'Digite seu texto aqui...',
        tabsize: 2,
        height: 250,
        toolbar: [
            ['style', ['style', 'bold', 'italic', 'underline', 'strikethrough', 'clear']],
            ['font', ['fontname', 'fontsize']],
            ['script', ['superscript', 'subscript']],
            ['color', ['color']],
            ['height', ['height']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link', 'table']],
            ['view', ['fullscreen', 'codeview']],
        ],
        lang: 'pt-BR',
    });

    // Torna a função saveModeloDocumento global
    window.saveModeloDocumento = function (successMessage) {
        $("#btnSalvar").on('click', function (event) {
            event.preventDefault();

            // Atribui o conteúdo do Summernote ao campo hidden antes de submeter
            $('#ViewModel_TextoDocumento').val($('#editor').summernote('code'));

            // Submete o formulário via AJAX para o método OnPost
            $.ajax({
                url: window.location.pathname, // Usa a URL atual da página
                type: 'POST',
                data: $("#ModeloDocumentoForm").serialize(),
                headers: {
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                success: function (response) {
                    if (response.success) {
                        abp.notify.success(response.message || successMessage, "Sucesso", { okText: "OK" });
                        setTimeout(() => {
                            window.location.href = abp.appPath + 'ModelosDocumentos';
                        }, 500);
                    } else {
                        // Exibe erros de validação
                        if (response.errors) {
                            displayValidationErrors(response.errors);
                        } else {
                            abp.notify.error(response.message || "Erro ao salvar o modelo de documento.", "Erro");
                        }
                    }
                },
                error: function () {
                    abp.notify.error("Erro interno do servidor. Tente novamente.", "Erro");
                }
            });
        });
    }

    // Função para exibir erros de validação
    function displayValidationErrors(errors) {
        // Limpa erros anteriores
        $('.field-validation-error').removeClass('field-validation-error').html('');
        $('.input-validation-error').removeClass('input-validation-error').attr('aria-invalid', 'false');

        // Exibe novos erros
        for (const fieldName in errors) {
            const fieldErrors = errors[fieldName];
            const $field = $(`[name="${fieldName}"]`);

            if ($field.length > 0) {
                $field.addClass('input-validation-error').attr('aria-invalid', 'true');
                const errorSpan = $(`span[data-valmsg-for="${fieldName}"]`);
                if (errorSpan.length > 0) {
                    errorSpan.addClass('field-validation-error');
                    errorSpan.html('<span>' + fieldErrors.join(', ') + '</span>');
                }
            }
        }
    }

    document.getElementById("btnCancelar").addEventListener("click", function (event) {
        event.preventDefault();
        window.history.back(); // Retorna para a página anterior no histórico do navegador
    });

});