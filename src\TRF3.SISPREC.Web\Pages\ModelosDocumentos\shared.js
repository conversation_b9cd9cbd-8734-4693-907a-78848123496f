$(function () {
    // Configuração do Summernote
    $('#editor').summernote({
        placeholder: 'Digite seu texto aqui...',
        tabsize: 2,
        height: 250,
        toolbar: [
            ['style', ['style', 'bold', 'italic', 'underline', 'strikethrough', 'clear']],
            ['font', ['fontname', 'fontsize']],
            ['script', ['superscript', 'subscript']],
            ['color', ['color']],
            ['height', ['height']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link', 'table']],
            ['view', ['fullscreen', 'codeview']],
        ],
        lang: 'pt-BR',
    });

    // Torna a função saveModeloDocumento global
    window.saveModeloDocumento = function (successMessage) {
        // Configura o formulário para capturar o conteúdo do Summernote antes do submit
        $("#ModeloDocumentoForm").on('submit', function () {
            // Captura o conteúdo do Summernote e atribui ao campo hidden
            $('#ViewModel_TextoDocumento').val($('#editor').summernote('code'));
        });

        // Configura o tratamento de sucesso para o formulário AJAX do ABP
        $("#ModeloDocumentoForm").on('abp-ajax-success', function () {
            abp.notify.success(successMessage, "Sucesso", { okText: "OK" });
            setTimeout(() => {
                window.location.href = abp.appPath + 'ModelosDocumentos';
            }, 500);
        });

        // Configura o tratamento de erro para o formulário AJAX do ABP
        $("#ModeloDocumentoForm").on('abp-ajax-error', function () {
            // O ABP já trata os erros de validação automaticamente
            // Este evento é para erros não tratados
            abp.notify.error("Erro ao salvar o modelo de documento.", "Erro");
        });
    }

    document.getElementById("btnCancelar").addEventListener("click", function (event) {
        event.preventDefault();
        window.history.back(); // Retorna para a página anterior no histórico do navegador
    });

});