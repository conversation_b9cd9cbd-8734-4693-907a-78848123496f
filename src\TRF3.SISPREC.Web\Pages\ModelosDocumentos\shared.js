$(function () {
    // Configuração do Summernote
    $('#editor').summernote({
        placeholder: 'Digite seu texto aqui...',
        tabsize: 2,
        height: 250,
        toolbar: [
            ['style', ['style', 'bold', 'italic', 'underline', 'strikethrough', 'clear']],
            ['font', ['fontname', 'fontsize']],
            ['script', ['superscript', 'subscript']],
            ['color', ['color']],
            ['height', ['height']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link', 'table']],
            ['view', ['fullscreen', 'codeview']],
        ],
        lang: 'pt-BR',
    });

    // Torna a função saveModeloDocumento global
    window.saveModeloDocumento = function (successMessage) {
        $("#btnSalvar").on('click', function (event) {
            event.preventDefault();

            // Executa a validação dos campos
            if (!validaCampo()) {
                // Interrompe o código se a validação falhar
                return;
            }

            //Obtém o JS Client Proxy do ModeloDocumentoAppService
            const modeloAppService = tRF3.sISPREC.modelosDocumentos.modeloDocumento;

            //Dto usado pelo client proxy
            let modeloDocumentoDto = {};

            //Preenche o DTO por meio dos campos do formulário
            $("#ModeloDocumentoForm").serializeArray().forEach(function (data) {
                if (data.value !== '')
                    //Obtém o nome correto esperado pelo AppService
                    modeloDocumentoDto[data.name.split('.').pop()] = data.value;
            });

            //Atribui a propriedade do conteúdo do documento
            modeloDocumentoDto.TextoDocumento = $('#editor').summernote('code');

            //Verifica se a ação será de criação ou atualização
            let acao = modeloDocumentoDto.ModeloDocumentoId
                ? modeloAppService.update(modeloDocumentoDto.ModeloDocumentoId, modeloDocumentoDto)
                : modeloAppService.create(modeloDocumentoDto);

            acao
                .then(() => {
                    abp.notify.success(successMessage, "Sucesso", { okText: "OK" });
                    setTimeout(() => {
                        window.location.href = abp.appPath + 'ModelosDocumentos';
                    }, 500);
                });
        });
    }

    document.getElementById("btnCancelar").addEventListener("click", function (event) {
        event.preventDefault();
        window.history.back(); // Retorna para a página anterior no histórico do navegador
    });

    function validaCampo() {
        // Valida o campo NomeModelo
        let $nomeModelo = $('#ViewModel_NomeModelo');
        let nomeModeloError = $nomeModelo.data('val-required');
        let isNomeModeloValid = $nomeModelo.val().trim() !== '';

        if (!isNomeModeloValid) {
            $nomeModelo.addClass('input-validation-error').attr('aria-invalid', 'true');
            let errorSpan = $('span[data-valmsg-for="' + $nomeModelo.attr('name') + '"]');
            errorSpan.addClass('field-validation-error');
            errorSpan.html('<span>' + nomeModeloError + '</span>');
        } else {
            $nomeModelo.removeClass('input-validation-error').attr('aria-invalid', 'false');
            let errorSpan = $('span[data-valmsg-for="' + $nomeModelo.attr('name') + '"]');
            errorSpan.removeClass('field-validation-error').html('');
        }

        // Valida o campo SetorId
        let $setorId = $('#ViewModel_SetorId');
        let setorIdError = $setorId.data('val-required');
        let isSetorIdValid = $setorId.val().trim() !== '';

        if (!isSetorIdValid) {
            $setorId.addClass('input-validation-error').attr('aria-invalid', 'true');
            let errorSpan = $('span[data-valmsg-for="' + $setorId.attr('name') + '"]');
            errorSpan.addClass('field-validation-error');
            errorSpan.html('<span>' + setorIdError + '</span>');
        } else {
            $setorId.removeClass('input-validation-error').attr('aria-invalid', 'false');
            let errorSpan = $('span[data-valmsg-for="' + $setorId.attr('name') + '"]');
            errorSpan.removeClass('field-validation-error').html('');
        }

        // Retorna true somente se ambos os campos forem válidos
        return isNomeModeloValid && isSetorIdValid;
    }

});