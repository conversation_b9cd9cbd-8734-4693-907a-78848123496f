using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using TRF3.SISPREC.VerificacaoSucumbencial;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoSucumbencialJob : AsyncBackgroundJob<VerificacaoSucumbencialArgs>, IVerificacaoSucumbencialJob
{
    private readonly IVerificacaoSucumbencialManager _verificacaoSucumbencialManager;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public VerificacaoSucumbencialJob(IVerificacaoSucumbencialManager verificacaoCnpjCpfManager, IUnitOfWorkManager unitOfWorkManager)
    {
        _verificacaoSucumbencialManager = verificacaoCnpjCpfManager;
        _unitOfWorkManager = unitOfWorkManager;
    }

    public override async Task ExecuteAsync(VerificacaoSucumbencialArgs args)
    {
        try
        {
            if (string.IsNullOrEmpty(args.NumeroProtocoloRequisicao))
            {
                Logger.LogError("Erro ao executar VerificacaoSucumbencialJob. NumeroProtocoloRequisicao inválido: NumeroProtocoloRequisicao: {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
                return;
            }

            using (var uow = _unitOfWorkManager.Begin(false, true))
            {
                await _verificacaoSucumbencialManager.ProcessarVerificacaoAsync(args.NumeroProtocoloRequisicao);
                await uow.CompleteAsync();
            }

        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoSucumbencialJob: NumeroProtocoloRequisicao: {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
            throw;
        }
    }
}

public class VerificacaoSucumbencialArgs : BaseBackgroundJobArgs
{
    public string NumeroProtocoloRequisicao { get; set; }

    public override string JobGroupName => VerificacaoRequisicoesSettings.VerificacaoSucumbencialJobGroupName;

    public VerificacaoSucumbencialArgs(string numeroProtocoloRequisicao)
    {
        NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
    }
}