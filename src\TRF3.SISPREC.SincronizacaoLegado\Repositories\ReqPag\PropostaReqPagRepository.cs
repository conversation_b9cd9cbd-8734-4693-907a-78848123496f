using Dapper;
using NUglify.Helpers;
using System.Data;
using TRF3.SISPREC.Apoio.Record;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Propostas.Dtos;
using TRF3.SISPREC.SincronizacaoLegado.Models.ReqPag;

namespace TRF3.SISPREC.SincronizacaoLegado.Repositories.ReqPag
{
    public class PropostaReqPagRepository : BaseReqPagRepository, IPropostaReqPagRepository
    {
        public PropostaReqPagRepository(IDbConnection connection, IDbTransaction transaction) : base(connection, transaction) { }

        public virtual async Task<IEnumerable<Proposta>> BuscaIndiceEntidade(Proposta propostaDTO)
        {

            var query = "Select * From proposta  where cod_cadast_entida = @cod_cadast_entida and cod_tipo_proced = @cod_tipo_proced and ano_propos = @ano_propos and num_propos = @num_propos";

            var parametros = new
            {
                cod_cadast_entida = propostaDTO.cod_cadast_entida,
                cod_tipo_proced = propostaDTO.cod_tipo_proced,
                ano_propos = propostaDTO.ano_propos,
                num_propos = propostaDTO.num_propos
            };

            return await _connection.QueryAsync<Proposta>(query, parametros, _transaction);

        }


        public virtual async Task<IEnumerable<Proposta>> BuscaIndice(Proposta propostaDTO)
        {

            var query = "Select * From proposta where cod_tipo_proced = @cod_tipo_proced and ano_propos = @ano_propos and num_propos = @num_propos";

            var parametros = new
            {
                cod_tipo_proced = propostaDTO.cod_tipo_proced,
                ano_propos = propostaDTO.ano_propos,
                num_propos = propostaDTO.num_propos
            };


            return await _connection.QueryAsync<Proposta>(query, parametros, _transaction);

        }

        /// <summary>
        /// Método criado para o SISPREC, para que seja feita a migração apenas dos RPVs dos ultimos 2 meses (contanto o mês atual) + propostas do próximo mês
        /// NÃO RETORNA PROPOSTA DE UNIDADES NÃO ORÇAMENTÁRIAS. QUANDO FOR IMPLEMENTADO CADASTRO/CARGA DE UNIDADES NÃO ORÇAMENTÁRIAS, DEVERÁ RETORNAR.
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<Proposta>> BuscaPropostasRPVDoisUltimosMesesEProximo()
        {
            var dataMesAtual = DateTime.Now;
            var dataMesAnterior = dataMesAtual.AddMonths(-1);
            var dataMesProximo = dataMesAtual.AddMonths(1);

            var query = "select p.* from reju.proposta p inner join reju.cadastro_pessoa pe on p.cod_cadast_entida = pe.cod_cadast_pessoa and pe.cod_tipo_pessoa in('O') " +//removido para reanalise os demais status,'E','R','M','L'
                        "where p.cod_tipo_proced = 'RPV' and " +
                        "((p.num_propos = @mesAnterior and p.ano_propos = @anoMesAnterior) OR " +
                        "(p.num_propos = @mesAtual and p.ano_propos = @anoMesAtual) OR " +
                        "(p.num_propos = @mesProximo and p.ano_propos = @anoMesProximo))";

            var parametros = new
            {
                mesAnterior = dataMesAnterior.Month.ToString(),
                anoMesAnterior = dataMesAnterior.Year.ToString(),
                mesAtual = dataMesAtual.Month.ToString(),
                anoMesAtual = dataMesAtual.Year.ToString(),
                mesProximo = dataMesProximo.Month.ToString(),
                anoMesProximo = dataMesProximo.Year.ToString(),
            };

            return await _connection.QueryAsync<Proposta>(query, parametros, _transaction);
        }



        /// <summary>
        /// Método criado para o SISPREC, para buscar propostas de tipo precatório com ano igual ou maior que o ano atual
        /// NÃO RETORNA PROPOSTA DE UNIDADES NÃO ORÇAMENTÁRIAS. QUANDO FOR IMPLEMENTADO CADASTRO/CARGA DE UNIDADES NÃO ORÇAMENTÁRIAS, DEVERÁ RETORNAR.
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<Proposta>> BuscaPropostasPCTAnoMaiorOuIgualAoAtual()
        {
            var query = "select p.* from reju.proposta p inner join reju.cadastro_pessoa pe on p.cod_cadast_entida = pe.cod_cadast_pessoa and pe.cod_tipo_pessoa in('O') " + //removido para reanalise os demais status,'E','R','M','L'
            " where cod_tipo_proced = @cod_tipo_proced and ano_propos >= @ano_propos";

            var parametros = new
            {
                cod_tipo_proced = ETipoProcedimentoRequisicao.PRC.ToString(),
                ano_propos = DateTime.Now.Year
            };

            return await _connection.QueryAsync<Proposta>(query, parametros, _transaction);
        }




        public virtual async Task UpdateFechaProposta(IList<Proposta> propostaDTO)
        {

            var query = "Update proposta set cod_situac_propos = @cod_situac_propos where cod_tipo_proced = @cod_tipo_proced " +
                " and cod_cadast_entida = @cod_cadast_entida and ano_propos = @ano_propos and num_propos = @num_propos";


            propostaDTO.ForEach(async item =>
            {
                var parametros = new
                {
                    cod_situac_propos = item.cod_situac_propos,
                    cod_tipo_proced = item.cod_tipo_proced,
                    cod_cadast_entida = item.cod_cadast_entida,
                    ano_propos = item.ano_propos,
                    num_propos = item.num_propos
                };

                await _connection.ExecuteAsync(query, parametros, _transaction);
            });
        }


        public virtual async Task<IEnumerable<Proposta>> BuscaEntidadeNaoFechada(Proposta propostaDTO)
        {

            var query = "Select * From proposta  where cod_tipo_proced = @cod_tipo_proced and ano_propos = @ano_propos and num_propos = @num_propos and cod_situac_propos <> 'F'";

            var parametros = new
            {
                cod_tipo_proced = propostaDTO.cod_tipo_proced,
                ano_propos = propostaDTO.ano_propos,
                num_propos = propostaDTO.num_propos
            };

            return await _connection.QueryAsync<Proposta>(query, parametros, _transaction);

        }


        public virtual async Task<IEnumerable<Proposta>> BuscaEntidade(Proposta propostaDTO, string Entidade)
        {

            var query =
                "Select cod_tipo_proced ,cod_cadast_entida ,ano_propos ,num_propos ,dat_atuali,val_minimo, " +
        "val_maximo,val_minimo_parcel,qtd_maxima_parcel_alimet,qtd_maxima_parcel_comum ,qtd_maxima_parcel_desapr_unico, " +
        "dat_indica_econom,cod_tipo_indica_econom ,cod_situac_propos ,dat_inicio_calcul_juros" +
                "  From proposta where cod_tipo_proced = @cod_tipo_proced and ano_propos = @ano_propos and num_propos = @num_propos";
            var parametros = new Dictionary<string, object>();

            parametros["cod_tipo_proced"] = propostaDTO.cod_tipo_proced;
            parametros["ano_propos"] = propostaDTO.ano_propos;
            parametros["num_propos"] = propostaDTO.num_propos;


            if (!Entidade.IsNullOrEmpty())
            {
                query += " and cod_cadast_entida = @cod_cadast_entida";
                parametros["cod_cadast_entida"] = Entidade.IsNullOrEmpty() ? 0 : Convert.ToInt32(Entidade);
            }

            var retorno = await _connection.QueryAsync(query + ";", parametros, _transaction);

            IList<Proposta> LPP_DTO = new List<Proposta>();
            if (retorno != null)
            {
                retorno.ForEach(item =>
                {
                    Proposta PP_DTO = new Proposta();
                    PP_DTO.cod_tipo_proced = item.cod_tipo_proced;
                    PP_DTO.cod_cadast_entida = UtilRecordHelper.RetornarValorIntOuZero(item.cod_cadast_entida);
                    PP_DTO.num_propos = UtilRecordHelper.RetornarValorIntOuZero(item.num_propos);
                    PP_DTO.ano_propos = UtilRecordHelper.RetornarValorIntOuZero(item.ano_propos);
                    PP_DTO.dat_atuali = item.dat_atuali;
                    PP_DTO.val_minimo = UtilRecordHelper.RetornarValorDoubleOuZero(item.val_minimo);
                    PP_DTO.val_maximo = UtilRecordHelper.RetornarValorDoubleOuZero(item.val_maximo);
                    PP_DTO.val_minimo_parcel = UtilRecordHelper.RetornarValorDoubleOuZero(item.val_minimo_parcel);
                    PP_DTO.qtd_maxima_parcel_alimet = UtilRecordHelper.RetornarValorIntOuZero(item.qtd_maxima_parcel_alimet);
                    PP_DTO.qtd_maxima_parcel_comum = UtilRecordHelper.RetornarValorIntOuZero(item.qtd_maxima_parcel_comum);
                    PP_DTO.qtd_maxima_parcel_desapr_unico = UtilRecordHelper.RetornarValorIntOuZero(item.qtd_maxima_parcel_desapr_unico);
                    PP_DTO.dat_indica_econom = item.dat_indica_econom;
                    PP_DTO.cod_tipo_indica_econom = item.cod_tipo_indica_econom;
                    PP_DTO.cod_situac_propos = item.cod_situac_propos;
                    PP_DTO.dat_inicio_calcul_juros = item.dat_inicio_calcul_juros;
                    LPP_DTO.Add(PP_DTO);
                });
            }
            return LPP_DTO;
        }
    }
}