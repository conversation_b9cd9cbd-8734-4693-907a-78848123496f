using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.IndicadorEconomicoTipos;
using TRF3.SISPREC.IndicadorEconomicoTipos.Dtos;
using TRF3.SISPREC.Unidades;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.Propostas;

public class IndexModel : SISPRECPageModel
{
    private readonly IUnidadeRepository _unidadeRepository;
    private readonly IIndicadorEconomicoTipoAppService _indicadorEconomicoTipoAppService;

    public IndexModel(IUnidadeRepository unidadeRepository, IIndicadorEconomicoTipoAppService indicadorEconomicoTipoAppService)
    {
        _unidadeRepository = unidadeRepository;
        _indicadorEconomicoTipoAppService = indicadorEconomicoTipoAppService;
    }

    public PropostaFilterInput PropostaFilter { get; set; } = new();

    public virtual async Task OnGetAsync()
    {
        var unidades = await _unidadeRepository.GetListAsync();

        PropostaFilter = new();
        PropostaFilter.UnidadeLookupList.AddRange(unidades.Select(t => new SelectListItem(t.Nome, t.UnidadeId.ToString())));

        PropostaFilter.SituacaoPropostaLookupList.AddRange(EnumExtensions.GetEnumSelectList<ESituacaoProposta>().ToList());

        var tiposIndicadoresEconomicos = await _indicadorEconomicoTipoAppService.GetListAsync(new IndicadorEconomicoTipoGetListInput());

        PropostaFilter.TipoIndicadoresEconomicos.AddRange(tiposIndicadoresEconomicos.Items.Select(t => new SelectListItem(t.Codigo, t.TipoIndicadorEconomicoId.ToString())));

        await Task.CompletedTask;
    }

    public class PropostaFilterInput
    {
        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Procedimento")]
        [DisplayOrder(1)]
        public ETipoProcedimentoRequisicao? TipoProcedimentoId { get; set; }

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Ano")]
        [DisplayOrder(2)]
        public int? AnoProposta { get; set; }

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Mês")]
        [SelectItems(nameof(MesesComItemVazio))]
        [DisplayOrder(3)]
        public int? MesProposta { get; set; }

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Situação Proposta")]
        [SelectItems(nameof(SituacaoPropostaLookupList))]
        [DisplayOrder(4)]
        public ESituacaoProposta? SituacaoProposta { get; set; }
        public List<SelectListItem> SituacaoPropostaLookupList { get; set; } =
        [
            new(string.Empty, null)
        ];

        [FormControlSize(AbpFormControlSize.Medium)]
        [Display(Name = "Unidade")]
        [SelectItems(nameof(UnidadeLookupList))]
        [DisplayOrder(5)]
        public int? UnidadeId { get; set; }
        public List<SelectListItem> UnidadeLookupList { get; set; } =
        [
            new(string.Empty, null)
        ];

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Tipo Indicador Econômico")]
        [SelectItems(nameof(TipoIndicadoresEconomicos))]
        [DisplayOrder(6)]
        public int? TipoIndicadorEconomicoId { get; set; }
        public List<SelectListItem> TipoIndicadoresEconomicos { get; set; } =
        [
            new(string.Empty, null)
        ];
    }
}