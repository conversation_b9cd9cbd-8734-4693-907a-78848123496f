using Microsoft.Extensions.DependencyInjection;
using TRF3.SISPREC.Assuntos;
using TRF3.SISPREC.Domain;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.IndicadorEconomicoTipos;
using TRF3.SISPREC.ProcessoOrigens;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicaoObservacoes;
using TRF3.SISPREC.REquisicoesExpedientesAdministrativos;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesPartes;
using TRF3.SISPREC.RequisicoesPropostaParcela;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesPropostas.Servicos;
using TRF3.SISPREC.RequisicoesProtocolos.AssuntosExecucoes;
using TRF3.SISPREC.RequisicoesVerificacoes;
using TRF3.SISPREC.SincronizacaoLegado;
using TRF3.SISPREC.SituacoesRequisicoesProtocolos;
using TRF3.SISPREC.UnidadesJudiciais;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;
using TRF3.SISPREC.RequisicaoEstornos;
using IRequisicaoEstornoRepository = TRF3.SISPREC.RequisicaoEstornos.IRequisicaoEstornoRepository;
using TRF3.SISPREC.RequisicoesPlanosOrcamentos;
using TRF3.SISPREC.RequisicoesVerificacoes.TiposVerificacoes;

namespace TRF3.SISPREC.RequisicoesProtocolos;

public class RequisicaoProtocoloManager : BaseDomainManager<RequisicaoProtocolo>, IRequisicaoProtocoloManager
{
    #region Read-Only Fields

    private IRequisicaoPropostaRepository _requisicaoPropostaRepository;
    private IPropostaRepository _propostaRepository;
    private readonly IReqPagUnitOfWork _reqPagUnitOfWork;
    private ISituacaoRequisicaoProtocoloRepository _situacaoRequisicaoProtocoloRepository;
    private IAssuntoRepository _assuntoRepository;
    private IUnitOfWorkManager _unitOfWorkManager;
    private IAssuntoExecucaoManager _assuntoExecucaoManager;
    private IRequisicaoParteManager _requisicaoParteManager;
    private IProcessoOrigemManager _processoOrigemManager;
    private IRequisicaoObservacaoManager _requisicaoObservacaoManager;
    private IUnidadeJudicialRepository _unidadeJudicialRepository;
    private IRequisicaoExpedienteAdministrativoManager _requisicaoExpedienteAdministrativoManager;
    private IRequisicaoVerificacaoManager _requisicaoVerificacaoManager;
    private List<IResponsabilidadeVerificacao> _handlers;
    private IRequisicaoEstornoRepository _requisicaEstornoRepository;
    private IImportarRequisicoesOcorrenciasService _requisicaoOCorrenciaService;

    #endregion

    #region Construtor
    public RequisicaoProtocoloManager(IRequisicaoProtocoloRepository repository, IReqPagUnitOfWork reqPagUnitOfWork) : base(repository)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _reqPagUnitOfWork = reqPagUnitOfWork ?? throw new ArgumentNullException(nameof(reqPagUnitOfWork));
        VerificaValidacao();
    }
    private async Task InjetandoDependencia()
    {
        var propostaRepository = LazyServiceProvider.GetRequiredService<IPropostaRepository>();
        var situacaoRequisicaoProtocoloRepository = LazyServiceProvider.GetRequiredService<ISituacaoRequisicaoProtocoloRepository>();
        var assuntoRepository = LazyServiceProvider.GetRequiredService<IAssuntoRepository>();
        var unitOfWorkManager = LazyServiceProvider.GetRequiredService<IUnitOfWorkManager>();
        var assuntoExecucaoManager = LazyServiceProvider.GetRequiredService<IAssuntoExecucaoManager>();
        var requisicaoParteManager = LazyServiceProvider.GetRequiredService<IRequisicaoParteManager>();
        var processoOrigemManager = LazyServiceProvider.GetRequiredService<IProcessoOrigemManager>();
        var requisicaoObservacaoManager = LazyServiceProvider.GetRequiredService<IRequisicaoObservacaoManager>();
        var unidadeJudicialRepository = LazyServiceProvider.GetRequiredService<IUnidadeJudicialRepository>();
        var requisicaoExpedienteAdministrativoManager = LazyServiceProvider.GetRequiredService<IRequisicaoExpedienteAdministrativoManager>();
        var requisicaoProposta = LazyServiceProvider.GetRequiredService<IRequisicaoPropostaRepository>();
        var requisicaoVerificacaoManager = LazyServiceProvider.GetRequiredService<IRequisicaoVerificacaoManager>();
        var requisicaoEstorno = LazyServiceProvider.GetRequiredService<IRequisicaoEstornoRepository>();
        var requisicoesOcorrenciaService = LazyServiceProvider.GetRequiredService<IImportarRequisicoesOcorrenciasService>();


        _propostaRepository = propostaRepository ?? throw new ArgumentNullException(nameof(propostaRepository));
        _situacaoRequisicaoProtocoloRepository = situacaoRequisicaoProtocoloRepository ?? throw new ArgumentNullException(nameof(situacaoRequisicaoProtocoloRepository));
        _assuntoRepository = assuntoRepository ?? throw new ArgumentNullException(nameof(assuntoRepository));
        _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
        _assuntoExecucaoManager = assuntoExecucaoManager ?? throw new ArgumentNullException(nameof(assuntoExecucaoManager));
        _requisicaoParteManager = requisicaoParteManager ?? throw new ArgumentNullException(nameof(requisicaoParteManager));
        _processoOrigemManager = processoOrigemManager ?? throw new ArgumentNullException(nameof(processoOrigemManager));
        _requisicaoObservacaoManager = requisicaoObservacaoManager ?? throw new ArgumentNullException(nameof(requisicaoObservacaoManager));
        _unidadeJudicialRepository = unidadeJudicialRepository ?? throw new ArgumentNullException(nameof(unidadeJudicialRepository));
        _requisicaoExpedienteAdministrativoManager = requisicaoExpedienteAdministrativoManager ?? throw new ArgumentNullException(nameof(requisicaoExpedienteAdministrativoManager));
        _requisicaoPropostaRepository = requisicaoProposta ?? throw new ArgumentNullException(nameof(requisicaoProposta));
        _requisicaoVerificacaoManager = requisicaoVerificacaoManager ?? throw new ArgumentNullException(nameof(requisicaoVerificacaoManager));
        _requisicaEstornoRepository = requisicaoEstorno ?? throw new ArgumentNullException(nameof(requisicaoEstorno));
        _requisicaoOCorrenciaService = requisicoesOcorrenciaService ?? throw new ArgumentNullException(nameof(requisicoesOcorrenciaService));
    }

    #endregion

    public async Task InserirImportacaoAsync(long propostaId, string numProtocolo)
    {
        await InjetandoDependencia();
        using (var uow = _unitOfWorkManager.Begin(true, true))
        {
            var listagemVerificacoes = new List<EVerificacaoRequisicaoTipo>();
            var proposta = (await _propostaRepository.PropostaComDetalhes()).AsEnumerable().FirstOrDefault(x => x.PropostaId == propostaId);

            if (proposta == null)
                throw new UserFriendlyException($"Proposta não encontrada! para Proposta id: {propostaId}");

            var requisicoesReqPag = await _reqPagUnitOfWork.RequisicaoProtocoloRepository.BuscaPorPropostaNaoImportadasPorProtocolo(proposta.TipoProcedimentoId, proposta.AnoProposta, proposta.MesProposta, numProtocolo);

            if (requisicoesReqPag == null)
                throw new UserFriendlyException($"Requisição não encontrada! para o Tipo procedimento id: {proposta.TipoProcedimentoId} e Ano proposta: {proposta.AnoProposta} e Mes proposta: {proposta.MesProposta} e Numero protocolo: {numProtocolo}");

            var situacaoRequisicao = await _situacaoRequisicaoProtocoloRepository.FirstOrDefaultAsync(s => s.SituacaoRequisicaoProtocoloId == requisicoesReqPag.cod_situac_requis);

            if (situacaoRequisicao == null)
                throw new UserFriendlyException($"Protocolo de Requisição de Situação não encontrada! para Código situação requisição: {requisicoesReqPag.cod_situac_requis}");

            var assunto = await _assuntoRepository.ObterAssuntoImportarReqPagAsync(requisicoesReqPag.cod_assunt);

            if (assunto == null)
                throw new UserFriendlyException($"Assunto não encontrado! para Assunto de código: {requisicoesReqPag.cod_assunt}");

            var unidadeJudicial = await _unidadeJudicialRepository.GetAsync(x => x.CodigoSiafi == requisicoesReqPag.cod_siafi_juizo_origem.ToString());

            if (unidadeJudicial == null)
                throw new UserFriendlyException($"Unidade Judicial não encontrado! para o Codigo siafi juizo origem: {requisicoesReqPag.cod_siafi_juizo_origem}");

            var requisicaoProtocolo = RequisicaoPropostaService.ToRequisicaoProtocolo(requisicoesReqPag, proposta.TipoProcedimento, situacaoRequisicao, assunto);
            var processosOrigens = await _processoOrigemManager.InserirImportacaoAsync(requisicoesReqPag, requisicaoProtocolo);

            requisicaoProtocolo.RequisicaoProcessosOrigem = processosOrigens;
            requisicaoProtocolo.AssuntoExecucoes = await _assuntoExecucaoManager.InserirImportacaoAsync(requisicoesReqPag, requisicaoProtocolo, assunto);
            requisicaoProtocolo.UnidadeJudicialId = unidadeJudicial.Seq_Unidad_Judici;
            requisicaoProtocolo.RequisicaoExpedienteAdministrativo = await _requisicaoExpedienteAdministrativoManager.InserirImportacaoAsync(numProtocolo);
            requisicaoProtocolo.RequisicaoOcorrencia = await LazyServiceProvider.GetRequiredService<IImportarRequisicoesOcorrenciasService>().CriaRequisicoesOcorrencias(numProtocolo);

            requisicaoProtocolo.RequisicaoPartes = await _requisicaoParteManager.InserirImportacaoAsync(numProtocolo, requisicaoProtocolo);

            requisicaoProtocolo.RequisicaoPlanoOrcamento = await (LazyServiceProvider.GetRequiredService<IImportarRequisicoesPlanosOrcamentosService>()).CriaRequisicaoPlanoOrcamento(numProtocolo);
            requisicaoProtocolo.RequisicaoObservacao = await _requisicaoObservacaoManager.InserirImportacaoAsync(numProtocolo);

            var requisicaoProposta = await ObterRequisicaoProposta(proposta, requisicoesReqPag, requisicaoProtocolo);

            await ObterRequisicaoEstorno(numProtocolo);


            foreach (var handler in _handlers)
            {

                handler.AdicionarVerificacoes(requisicaoProtocolo, listagemVerificacoes);

            }

            await _requisicaoOCorrenciaService.ImportarPrevento(numProtocolo);

            await _requisicaoVerificacaoManager.InserirPorRequisicao(requisicaoProtocolo.NumeroProtocoloRequisicao, listagemVerificacoes);
            await _requisicaoPropostaRepository.InsertAsync(requisicaoProposta, true);
            await uow.CompleteAsync();
        }
    }

    private async Task ObterRequisicaoEstorno(string numProtocolo)
    {
        var requisicaoEstornoReqpag = await _reqPagUnitOfWork.RequisicaoEstornoRepository.BuscaPorRequis(numProtocolo);
        if (requisicaoEstornoReqpag != null)
        {
            var requisicaoEstorno = new RequisicaoEstorno
            {
                CodigoBeneficiario = requisicaoEstornoReqpag.cod_benefi,
                DataHoraProtocoloRequisicao = (DateTime)requisicaoEstornoReqpag.dat_hora_protoc_requis,
                DataRecolhimentoConta = (DateTime)requisicaoEstornoReqpag.dat_recolh_conta,
                NomeBeneficiario = requisicaoEstornoReqpag.nom_benefi,
                NumeroBanco = requisicaoEstornoReqpag.num_banco,
                NumeroContaCorrente = requisicaoEstornoReqpag.num_conta_corren,
                NumeroProtocoloRequisicaoId = numProtocolo,
                NumeroRequisicaoOriginal = requisicaoEstornoReqpag.num_requis.Trim(),
                ValorRecolhimentoConta = (double)requisicaoEstornoReqpag.val_recolh_conta
            };

            await _requisicaEstornoRepository.InsertAsync(requisicaoEstorno);
        }
    }

    private async Task<RequisicaoProposta> ObterRequisicaoProposta(Proposta? proposta, SincronizacaoLegado.Models.ReqPag.RequisicaoProtocoloImportacao requisicaoReqPag, RequisicaoProtocolo requisicaoProtocolo)
    {
        var indicadorTipoId = requisicaoReqPag.cod_tipo_indica_econom != null
                ? await LazyServiceProvider.GetRequiredService<IIndicadorEconomicoTipoRepository>()
                    .FirstOrDefaultAsync(x => x.Codigo!.Equals(requisicaoReqPag.cod_tipo_indica_econom))
                : null;
        if (requisicaoReqPag.cod_tipo_indica_econom != null && indicadorTipoId == null)
        {
            throw new UserFriendlyException($"Não foi possível encontrar um tipo de indicador econômico com o código {requisicaoReqPag.cod_tipo_indica_econom}");
        }

        return new RequisicaoProposta
        {
            RequisicaoProtocolo = requisicaoProtocolo,
            Proposta = proposta,
            SituacaoRequisicaoProposta = requisicaoReqPag.cod_situac_requis_propos.Parse(),
            PropostaInicial = requisicaoReqPag.ide_propos_inicia.ParseToBool(),
            NumBanco = requisicaoReqPag.num_banco,
            NumAgencia = requisicaoReqPag.num_agenci,
            NumContaCorrente = requisicaoReqPag.num_conta_corren,
            IndicadorEconomicoTipoId = indicadorTipoId?.TipoIndicadorEconomicoId,
            NumeroProtocoloRequisicao = requisicaoReqPag.num_protoc_requis.Trim(),
            PropostaId = proposta.PropostaId,
            RequisicaoPropostaParcela = await ObterParcelas(requisicaoReqPag.num_protoc_requis)
        };
    }

    private async Task<List<RequisicaoPropostaParcela>> ObterParcelas(string numProtocolo)
    {
        var propostaParcela = await _reqPagUnitOfWork.RequisPropostaParcelaRepository.BuscaPorRequisicaoLista(numProtocolo);

        var requisicaoPropostaParcela = new List<RequisicaoPropostaParcela>();
        foreach (var item in propostaParcela)
        {
            int? tipoIndicadorId = null;

            // Se o código do indicador NÃO for nulo nem vazio, buscar no banco
            if (!string.IsNullOrWhiteSpace(item.cod_tipo_indica_econom))
            {
                var indicadorTipo = await LazyServiceProvider.GetRequiredService<IIndicadorEconomicoTipoRepository>()
                    .FirstOrDefaultAsync(x => x.Codigo!.Equals(item.cod_tipo_indica_econom));

                if (indicadorTipo == null)
                    throw new UserFriendlyException($"Não foi possível encontrar tipo de indicador econômico com código {item.cod_tipo_indica_econom}.");

                tipoIndicadorId = indicadorTipo.TipoIndicadorEconomicoId;
            }

            var reqParcelaProposta = new RequisicaoPropostaParcela
            {
                RequisicaoProposta = null,
                IndicadorEconomicoTipo = null,
                NumeroProtocoloRequisicaoId = numProtocolo,
                DataCalculoParcelaId = (DateTime)item.dat_calcul_parcel,
                DataIndicacaoEconomica = (DateTime)item.dat_indica_econom,
                TipoIndicadorEconomicoId = tipoIndicadorId,
                QuantidadeMaximaParcela = item.qtd_maxima_parcel,
                QuantidadeParcela = item.qtd_parcel,
                ValorAtualiExerciCorrente = (decimal?)item.val_atuali_exerci_corren,
                ValorAtualizadoExerciAnterior = (decimal?)item.val_atuali_exerci_anteri,
                ValorAtualizadoDeducaIndivi = (decimal?)item.val_atuali_deduca_indivi,
                ValorAtualizadoReqPartePss = (decimal?)item.val_atuali_requis_parte_pss,
                ValorAtualizadoTotal = (decimal)item.val_atuali_total,
                ValorAtualizadoTotalP = (decimal?)item.val_atuali_total_p,
                ValorAtualizadoTotalJ = (decimal?)item.val_atuali_total_j,
                ValorIndicadorEconomico = (decimal)item.val_indica_econom,
                ValorJurosMora = (decimal?)item.val_juros_mora,
                ValorMinimoParcela = (decimal)item.val_minimo_parcel
            };
            requisicaoPropostaParcela.Add(reqParcelaProposta);
        }
        return requisicaoPropostaParcela;
    }


    private void VerificaValidacao()
    {
        _handlers = new List<IResponsabilidadeVerificacao>
        {
            new VerificacaoCpfCnpjHandler(),
            new VerificacaoAjuizamentoHandler(),
            new VerificacaoOrgaoPssHandler(),
            new VerificacaoPeritoAdvogadoHandler(),
            new VerificacaoPeritoCNPJHandler(),
            new VerificacaoSucumbencialHandler(),
            new VerificacaoNomePartesHandler(),
            new VerificacaoComplementarIgualHandler(),
            new VerificacaoSuplementarIgualHandler(),
            new VerificacaoIncontroversoMenorIgualHandler(),
            new VerificaTotalIncontroversoHandler(),
            new VerificacaoTotalIncontroversoNaoSucumbencialHandler(),
            new VerificacaoTotalIncontroversoSucumbencialHandler()
        };
    }
}
