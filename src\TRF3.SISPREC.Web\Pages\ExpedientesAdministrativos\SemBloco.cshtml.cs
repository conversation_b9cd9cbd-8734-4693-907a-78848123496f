using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Web.Pages.FilterInputs;

namespace TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos
{
    public class SemBlocoModel : SISPRECPageModel
    {
        public ExpedienteAdministrativoFilterInput ExpedienteAdministrativoFilter { get; set; } = new();

        public virtual async Task OnGetAsync()
        {
            ExpedienteAdministrativoFilter
                .TipoExpedienteLookupList
                .AddRange([.. EnumExtensions.GetEnumSelectList<ETipoExpedienteAdministrativo>().OrderBy(x => x.Text)]);

            await Task.CompletedTask;

            await Task.CompletedTask;
        }
    }

}
