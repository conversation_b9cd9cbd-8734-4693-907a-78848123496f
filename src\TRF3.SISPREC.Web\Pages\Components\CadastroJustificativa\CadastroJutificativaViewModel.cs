using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.RequisicaoJustificativas.Dtos;


namespace TRF3.SISPREC.Web.Pages.Components.CadastroJustificativas
{
    [ExcludeFromCodeCoverage]
    public class CadastroJutificativaViewModel
    {
        [DisplayName("Procedimento")]
        public string? Procedimento { get; set; }

        [DisplayName("Complemento do Motivo Selecionado")]
        public string? ComplementoMotivo { get; set; }

        [DisplayName("Tipo Análise")]
        public string? TipoAnalise { get; set; }

        [Required(ErrorMessage = "O campo Decisão é obrigatório.")]
        [DisplayName("Decisão")]
        public string? Decisao { get; set; }

        [Required(ErrorMessage = "O campo Motivo é obrigatório.")]
        [DisplayName("Motivo")]
        public string? Motivo { get; set; }

        [DisplayName("Observações para geração do espelho")]
        public string? Observacoes { get; set; }

        public List<RequisicaoComparadaDto>? RequisicoesComparadas { get; set; }

        #region SelectListItem

        public List<SelectListItem>? DecisaoList { get; set; }
        public List<string>? RequisicaoList { get; set; }
        public List<SelectListItem>? MotivoList { get; set; } = [new(string.Empty, null)];

        #endregion

        public CadastroJutificativaViewModel()
        {
            DecisaoList =
            [
                new(string.Empty, string.Empty),
                new("LIBERAR", ((int)EDescricaoAcaoTipo.LIBERACAO).ToString()),
                new("CANCELAR", ((int)EDescricaoAcaoTipo.CANCELAMENTO).ToString())
            ];
        }
    }
}