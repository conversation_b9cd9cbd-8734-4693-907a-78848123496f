@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.UnidadesEquivalentes
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Unidades Equivalentes";
    PageLayout.Content.MenuItemName = SISPRECMenus.UnidadesEquivalentes;
}

@section scripts
{
    <abp-script src="/js/util.js" />
    <abp-script src="/Pages/UnidadesEquivalentes/index.js" />
    <abp-script src="/Pages/UnidadesEquivalentes/createModal.js" />    
}

@section styles {
    <abp-style src="/Pages/UnidadesEquivalentes/index.css" />
}

<abp-card>
    <abp-card>
        <abp-card-header>
            <abp-row class="justify-content-between align-items-center">
                <abp-column>
                    <a abp-collapse-id="UnidadeEquivalenteCollapse" class="text-secondary">Filtrar</a>
                </abp-column>
                <abp-column class="text-end">
                    <abp-button id="NewUnidadeEquivalenteButton"
                                text="Novo"
                                icon="plus"
                                button-type="Primary" />
                </abp-column>
            </abp-row>
        </abp-card-header>
        <abp-card-body>
            <form abp-model="UnidadeEquivalenteFilter" id="UnidadeEquivalenteFilter" required-symbols="false" column-size="_3">
                <abp-collapse-body id="UnidadeEquivalenteCollapse">
                    <abp-row>
                        <abp-column>
                            <abp-input asp-for="UnidadeEquivalenteFilter.CodigoSiafi" />
                        </abp-column>

                        <abp-column>
                            <abp-input asp-for="UnidadeEquivalenteFilter.NomeUnidade" />
                        </abp-column>

                        <abp-column>
                            <abp-select asp-for="UnidadeEquivalenteFilter.Ativo"></abp-select>
                        </abp-column>
                    </abp-row>
                </abp-collapse-body>
            </form>
            <abp-table striped-rows="true" id="UnidadeEquivalenteTable" class="nowrap" responsive-sm="true" />
        </abp-card-body>
    </abp-card>
</abp-card>
