import { alertaBotoesInvertidos } from '/js/util.js';
abp.modals.createPeritoModal = function () {
    function initModal(modalManager) {
        let $form = modalManager.getForm();
        let numeroCpfCnpj = $form.find('#ViewModel_NumeroCnpjCpf');
        let verificacaoTipoId = $form.find('#ViewModel_VerificacaoTipoId');

        numeroCpfCnpj.on('blur', function () {
            tratarEventoBlur($form, numeroCpfCnpj, modalManager);
        });

        verificacaoTipoId.on('change', function () {
            tratarEventoBlur($form, numeroCpfCnpj, modalManager);
        });

        numeroCpfCnpj.on('input', function () {
            aplicarMascara($(this));
        });
    }

    async function tratarEventoBlur($form, numeroCpfCnpj, modalManager) {
        let numeroDocumento = numeroCpfCnpj.val().replace(/\D/g, ''); // Remove tudo que não for número

        if (!numeroDocumento) return;

        let nome = $form.find('#ViewModel_NomePessoa');
        let verificacaoTipoId = $form.find('#ViewModel_VerificacaoTipoId').val();

        const service = tRF3.sISPREC.peritos.perito;
        let perito = await service.getPeritoPorCpfCnpj(numeroDocumento, verificacaoTipoId);

        if (perito) {
            await tratarPeritoExistente(perito, modalManager, numeroCpfCnpj, nome, $form);
        } else {
            await buscarEPreencherNome(numeroDocumento, nome, service);
        }
    }

    async function tratarPeritoExistente(perito, modalManager, numeroCpfCnpj, nome, $form) {
        if (perito.ativo) {
            abp.message.error('Perito já cadastrado para este tipo de verificação e ativo');
        } else {
            alertaBotoesInvertidos('Você tem certeza?', 'Perito já cadastrado para este tipo de verificação, porém está inativo. Deseja reativá-lo?', 'warning')
                .then((result) => {
                    if (result.isConfirmed) {
                        alternarStatusPerito(perito, modalManager, numeroCpfCnpj, nome, $form);
                    }
                });
        }
    }

    async function alternarStatusPerito(perito, modalManager, numeroCpfCnpj, nome, $form) {
        const service = tRF3.sISPREC.peritos.perito;

        abp.ui.block({ elm: 'body', busy: true });
        try {
            await service.ativarDesativar(perito.peritoId);
            abp.notify.info(perito.ativo ? 'Perito desativado com sucesso!' : 'Perito reativado com sucesso!');
        } finally {
            numeroCpfCnpj.val('');
            nome.val('');
            $form.find('#ViewModel_VerificacaoTipoId').val('4');
            modalManager.close();
            $('#PeritoTable').DataTable().ajax.reload();
            abp.ui.unblock();
        }
    }

    async function buscarEPreencherNome(numeroDocumento, nome, service) {
        if (numeroDocumento.length === 11 || numeroDocumento.length === 14) {
            try {
                let result = numeroDocumento.length === 11
                    ? await service.obterNomePorCPF(numeroDocumento)
                    : await service.obterNomePorCNPJ(numeroDocumento);
                nome.val(result); // Preenche o campo com o nome obtido
            } catch (error) {
                console.error('Erro ao obter o nome:', error);
                alert('Não foi possível obter o nome. Tente novamente.');
            }
        }
    }

    function aplicarMascara(input) {
        let numeroDocumento = input.val().replace(/\D/g, ''); // Remove tudo que não for número

        if (numeroDocumento.length <= 11) {
            // Aplica máscara de CPF
            input.val(numeroDocumento.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4'));
        } else if (numeroDocumento.length <= 14) {
            // Aplica máscara de CNPJ
            input.val(numeroDocumento.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5'));
        }
    }

    return {
        initModal: initModal
    };
};
