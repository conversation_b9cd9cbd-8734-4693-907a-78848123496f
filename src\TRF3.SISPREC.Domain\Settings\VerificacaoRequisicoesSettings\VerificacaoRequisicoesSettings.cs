using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

namespace TRF3.SISPREC.Settings.VerificacaoRequisicoes;

[ExcludeFromCodeCoverage]
public static class VerificacaoRequisicoesSettings
{
    public const string VerificacaoCpfCnpjAtiva = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_CPF_CNPJ);
    public const string VerificacaoAjuizamentoAtiva = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_AJUIZAMENTO);
    public const string VerificacaoOrgaoPssAtiva = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_ORGAO_PSS);
    public const string VerificacaoPeritoAdvogadoAtiva = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PERITO_ADVOGADO);
    public const string VerificacaoPeritoCnpjAtiva = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PERITO_CNPJ);
    public const string VerificacaoSucumbencialAtiva = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_SUCUMBENCIAL);
    public const string VerificacaoNomePartesAtiva = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_NOME_PARTES);
    public const string VerificacaoComplementarIgualAtiva = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_COMPLEMENTAR_IGUAL);
    public const string VerificacaoSuplementarIgualAtiva = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_SUPLEMENTAR_IGUAL);
    public const string VerificacaoIncontroversoMenorIgualAtiva = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_INCONTROVERSO_MENOR_IGUAL);
    public const string VerificacaoPrevencaoTipo11Ativa = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_11);
    public const string VerificacaoPrevencaoTipo12Ativa = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_12);
    public const string VerificacaoPrevencaoTipo21Ativa = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_21);
    public const string VerificacaoPrevencaoTipo22Ativa = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_22);
    public const string VerificacaoPrevencaoTipo23Ativa = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_23);
    public const string VerificacaoPrevencaoTipo24Ativa = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_24);
    public const string VerificacaoPrevencaoTipo31Ativa = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_31);
    public const string VerificacaoPrevencaoTipo32Ativa = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_32);
    public const string VerificacaoPrevencaoTipo34Ativa = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_34);
    public const string VerificacaoPrevencaoTipo35Ativa = nameof(EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_35);


    /* Jobs que não podem rodar em paralelo atribui nome do job e do grupo */
    public const string EnfileiraVerificacoesPeriodicoJobName = nameof(IEnfileiraVerificacoesPeriodicoJob);
    public const string EnfileiraVerificacoesPeriodicoJobGroupName = nameof(IEnfileiraVerificacoesPeriodicoJob);

    /* Jobs que podem rodar em paralelo atribui somente nome do grupo */
    public const string ConsultaCnpjCpfJobGroupName = nameof(IConsultaCnpjCpfJob);
    public const string VerificacaoAjuizamentoJobGroupName = nameof(IVerificacaoAjuizamentoJob);
    public const string VerificacaoComplementarIgualJobGroupName = nameof(IVerificacaoComplementarIgualJob);
    public const string VerificacaoSuplementarIgualJobGroupName = nameof(IVerificacaoSuplementarIgualJob);
    public const string VerificacaoIncontroversoMenorIgualJobGroupName = nameof(IVerificacaoIncontroversoMenorIgualJob);
    public const string VerificacaoPeritoAdvogadoJobGroupName = nameof(IVerificacaoPeritoAdvogadoJob);
    public const string VerificacaoPeritoCnpjJobGroupName = nameof(IVerificacaoPeritoCnpjJob);
    public const string VerificacaoPrevencaoTipo21JobGroupName = nameof(IVerificacaoPrevencaoTipo21Job);
    public const string VerificacaoPrevencaoTipo22JobGroupName = nameof(IVerificacaoPrevencaoTipo22Job);
    public const string VerificacaoPrevencaoTipo23JobGroupName = nameof(IVerificacaoPrevencaoTipo23Job);
    public const string VerificacaoPrevencaoTipo24JobGroupName = nameof(IVerificacaoPrevencaoTipo24Job);
    public const string VerificacaoPrevencaoTipo31JobGroupName = nameof(IVerificacaoPrevencaoTipo31Job);
    public const string VerificacaoPrevencaoTipo32JobGroupName = nameof(IVerificacaoPrevencaoTipo32Job);
    public const string VerificacaoPrevencaoTipo34JobGroupName = nameof(IVerificacaoPrevencaoTipo34Job);
    public const string VerificacaoPrevencaoTipo35JobGroupName = nameof(IVerificacaoPrevencaoTipo35Job);
    public const string VerificacaoSucumbencialJobGroupName = nameof(IVerificacaoSucumbencialJob);
    public const string VerificacaoOrgaoPssJobGroupName = nameof(IVerificacaoOrgaoPssJob);
    public const string VerificacaoNomeParteJobGroupName = nameof(IVerificacaoNomeParteJob);

    public static string ObterNomeConfiguracaoPorTipoVerificacao(EVerificacaoRequisicaoTipo verificacaoRequisicaoTipo)
    {
        return verificacaoRequisicaoTipo switch
        {
            EVerificacaoRequisicaoTipo.VERIFICACAO_CPF_CNPJ => VerificacaoCpfCnpjAtiva,
            EVerificacaoRequisicaoTipo.VERIFICACAO_AJUIZAMENTO => VerificacaoAjuizamentoAtiva,
            EVerificacaoRequisicaoTipo.VERIFICACAO_ORGAO_PSS => VerificacaoOrgaoPssAtiva,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PERITO_ADVOGADO => VerificacaoPeritoAdvogadoAtiva,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PERITO_CNPJ => VerificacaoPeritoCnpjAtiva,
            EVerificacaoRequisicaoTipo.VERIFICACAO_SUCUMBENCIAL => VerificacaoSucumbencialAtiva,
            EVerificacaoRequisicaoTipo.VERIFICACAO_NOME_PARTES => VerificacaoNomePartesAtiva,
            EVerificacaoRequisicaoTipo.VERIFICACAO_COMPLEMENTAR_IGUAL => VerificacaoComplementarIgualAtiva,
            EVerificacaoRequisicaoTipo.VERIFICACAO_SUPLEMENTAR_IGUAL => VerificacaoSuplementarIgualAtiva,
            EVerificacaoRequisicaoTipo.VERIFICACAO_INCONTROVERSO_MENOR_IGUAL => VerificacaoIncontroversoMenorIgualAtiva,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_11 => VerificacaoPrevencaoTipo11Ativa,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_12 => VerificacaoPrevencaoTipo12Ativa,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_21 => VerificacaoPrevencaoTipo21Ativa,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_22 => VerificacaoPrevencaoTipo22Ativa,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_23 => VerificacaoPrevencaoTipo23Ativa,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_24 => VerificacaoPrevencaoTipo24Ativa,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_31 => VerificacaoPrevencaoTipo31Ativa,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_32 => VerificacaoPrevencaoTipo32Ativa,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_34 => VerificacaoPrevencaoTipo34Ativa,
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_35 => VerificacaoPrevencaoTipo35Ativa,
            _ => throw new ArgumentException("Tipo de verificação não encontrado para a configuração informada.")
        };
    }
}