using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.ArquivoServices;
using TRF3.SISPREC.PDFServices;
using TRF3.SISPREC.RequisicoesDocumentos;
using TRF3.SISPREC.RequisicoesDocumentos.Servicos;
using TRF3.SISPREC.RequisicoesProcessosOrigens;

namespace TRF3.SISPREC.RequisicaoDocumentos
{
    public class RequisicaoDocumentoServiceTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
    {
        private readonly IRequisicaoProcessoOrigemRepository _requisicaoProcessoOrigenRepository;
        private readonly IRequisicaoDocumentoRepository _repository;
        private readonly IRequisicaoDocumentoManager _manager;
        private readonly IPdfFileGeneratorService _pdfGenerator;
        private readonly IArquivoService _arquivoService;
        private readonly ILogger<RequisicaoDocumentoService> _logger;
        private readonly RequisicaoDocumentoService _service;

        public RequisicaoDocumentoServiceTests()
        {
            // Criando os mocks
            _requisicaoProcessoOrigenRepository = Substitute.For<IRequisicaoProcessoOrigemRepository>();
            _repository = Substitute.For<IRequisicaoDocumentoRepository>();
            _manager = Substitute.For<IRequisicaoDocumentoManager>();
            _pdfGenerator = Substitute.For<IPdfFileGeneratorService>();
            _arquivoService = Substitute.For<IArquivoService>();
            _logger = Substitute.For<ILogger<RequisicaoDocumentoService>>();

            // Instanciando o serviço diretamente
            _service = new RequisicaoDocumentoService(
                _requisicaoProcessoOrigenRepository,
                _repository,
                _manager,
                _pdfGenerator,
                _arquivoService
            );

            // Configura o logger mockado usando o extension method
            _service.ConfigurarLoggerMockado(_logger);
        }

        [Fact]
        public async Task GerarEspelhoRequisicaoAsync_SemNumeroRequisicaoComparada_DeveGerarNomeECaminhoCorretos()
        {
            // Arrange
            var numeroRequisicao = "12345";
            var observacao = "Observação teste";
            var procedimento = "RPV";
            var ano = 2024;
            var mes = 03;
            var nomeArquivoEsperado = $"{numeroRequisicao}-E-Espelho.pdf";
            var caminhoEsperado = $"requisicoes/{procedimento}/{ano}/{mes.ToString("D2")}/{numeroRequisicao}/{nomeArquivoEsperado}";

            _pdfGenerator.GenerateFileAsync(numeroRequisicao, observacao).Returns(new byte[0]);

            _repository
                .GetDadosEspelhoRequisicaoAsync(Arg.Any<string>())
                .Returns(Task.FromResult(new RequisicaoDocumentoModel()));

            _requisicaoProcessoOrigenRepository
                .GetDadosOriginarios(Arg.Any<string>())
                .Returns(Task.FromResult((IEnumerable<OriginariosModel>)new List<OriginariosModel>()));

            // Act
            await _service.GerarEspelhoRequisicaoAsync(numeroRequisicao, procedimento, ano, mes, observacao);

            // Assert
            await _arquivoService.Received(1).UploadAsync(Arg.Is<MemoryStream>(s => s.Length == 0), caminhoEsperado);
            await _manager.Received(1).InserirAsync(Arg.Is<RequisicaoDocumento>(d =>
                d.NomeDocumento == nomeArquivoEsperado &&
                d.Path == caminhoEsperado &&
                d.NumeroProtocoloRequisicao == numeroRequisicao &&
                !d.IsDeleted), true);
        }

        [Fact]
        public async Task GerarEspelhoRequisicaoAsync_ComNumeroRequisicaoComparada_DeveGerarNomeECaminhoCorretos()
        {
            // Arrange
            var numeroRequisicao = "12345";
            var numeroRequisicaoPrincipal = "67890";
            var observacao = "Observação teste";
            var procedimento = "ProcedimentoTeste";
            var ano = 2024;
            var mes = 03;
            var nomeArquivoEsperado = $"{numeroRequisicaoPrincipal}-E-{numeroRequisicao}-Espelho.pdf";
            var caminhoEsperado = $"requisicoes/{procedimento}/{ano}/{mes.ToString("D2")}/{numeroRequisicaoPrincipal}/{nomeArquivoEsperado}";

            _pdfGenerator.GenerateFileAsync(numeroRequisicao, observacao).Returns(new byte[0]);

            _repository
                .GetDadosEspelhoRequisicaoAsync(Arg.Any<string>())
                .Returns(Task.FromResult(new RequisicaoDocumentoModel()));

            _requisicaoProcessoOrigenRepository
                .GetDadosOriginarios(Arg.Any<string>())
                .Returns(Task.FromResult((IEnumerable<OriginariosModel>)new List<OriginariosModel>()));

            // Act
            await _service.GerarEspelhoRequisicaoAsync(numeroRequisicao, procedimento, ano, mes, observacao, numeroRequisicaoPrincipal);

            // Assert
            await _arquivoService.Received(1).UploadAsync(Arg.Is<MemoryStream>(s => s.Length == 0), caminhoEsperado);
            await _manager.Received(1).InserirAsync(Arg.Is<RequisicaoDocumento>(d =>
                d.NomeDocumento == nomeArquivoEsperado &&
                d.Path == caminhoEsperado &&
                d.NumeroProtocoloRequisicao == numeroRequisicaoPrincipal &&
                !d.IsDeleted), true);
        }

        [Fact]
        public async Task GerarEspelhoRequisicaoAsync_NumeroRequisicaoNuloOuVazio_DeveLancarArgumentException()
        {
            // Arrange
            var numeroRequisicao = "";
            var observacao = "Observação teste";
            var procedimento = "ProcedimentoTeste";
            var ano = 2024;
            var mes = 05;

            // Act
            var act = async () => await _service.GerarEspelhoRequisicaoAsync(numeroRequisicao, procedimento, ano, mes, observacao);

            // Assert
            await Should.ThrowAsync<ArgumentException>(act);

            // Verifica se o logger foi chamado com a mensagem de erro apropriada
            _logger.Received(1).LogError(
                Arg.Any<Exception>(),
                "Erro ao gerar espelho da requisição - Número da requisição não informado."
            );
        }
    }
}

