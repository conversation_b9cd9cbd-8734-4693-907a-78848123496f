﻿using System;
using System.ComponentModel;
using Volo.Abp.Application.Dtos;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.RequisicoesDocumentos.Dtos;

[Serializable]
public class RequisicaoDocumentoGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Documento Requisição Id")]
    public long? RequisicaoDocumentoId { get; set; }

    [Display(Name = "Nome do Documento")]
    public string? NomeDocumento { get; set; }

    [Display(Name = "Caminho do Arquivo")]
    public string? Path { get; set; }

    [Display(Name = "Data de Criação")]
    public DateTime? DataCriacao { get; set; }

    [Display(Name = "Numero do Protocolo Requisição")]
    public string? NumeroProtocoloRequisicao { get; set; }

    [Display(Name = "Deletado")]
    public bool? IsDeleted { get; set; }

}