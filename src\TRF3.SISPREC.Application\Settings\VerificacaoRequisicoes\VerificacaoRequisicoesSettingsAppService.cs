using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.RequisicoesVerificacoes.Jobs;
using Volo.Abp.SettingManagement;

namespace TRF3.SISPREC.Settings.VerificacaoRequisicoes;

[Authorize(Roles = SISPRECPermissoes.Perfil.AdminTI)]
public class VerificacaoRequisicoesSettingsAppService(ISettingManager settingManager, IBackgroundJobsService backgroundJobsService) : SISPRECBaseSettingsAppService(settingManager, backgroundJobsService), IVerificacaoRequisicoesSettingsAppService
{
    public async Task AlterarVerificacaoSuplementarIgualJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualJobGroupName, VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualAtiva);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualJobGroupName, VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualAtiva);
    }

    public async Task AlterarVerificacaoComplementarIgualJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoComplementarIgualJobGroupName, VerificacaoRequisicoesSettings.VerificacaoComplementarIgualAtiva);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoComplementarIgualJobGroupName, VerificacaoRequisicoesSettings.VerificacaoComplementarIgualAtiva);
    }

    public async Task AlterarVerificacaoIncontroversoMenorIgualJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualJobGroupName, VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualAtiva);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualJobGroupName, VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualAtiva);
    }

    public async Task AlterarVerificacaoCpfCnpjJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.ConsultaCnpjCpfJobGroupName, VerificacaoRequisicoesSettings.VerificacaoCpfCnpjAtiva);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.ConsultaCnpjCpfJobGroupName, VerificacaoRequisicoesSettings.VerificacaoCpfCnpjAtiva);
    }

    public async Task AlterarVerificacaoAjuizamentoJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoAjuizamentoJobGroupName, VerificacaoRequisicoesSettings.VerificacaoAjuizamentoAtiva);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoAjuizamentoJobGroupName, VerificacaoRequisicoesSettings.VerificacaoAjuizamentoAtiva);
    }

    public async Task AlterarVerificacaoPeritoAdvogadoJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoJobGroupName, VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoAtiva);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoJobGroupName, VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoAtiva);
    }

    public async Task AlterarVerificacaoPrevencaoTipo21Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo21JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo21Ativa);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo21JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo21Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo22Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo22JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo22Ativa);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo22JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo22Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo23Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo23JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo23Ativa);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo23JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo23Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo24Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo24JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo24Ativa);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo24JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo24Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo32Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo32JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo32Ativa);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo32JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo32Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo35Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo35JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo35Ativa);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo35JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo35Ativa);
    }

    public async Task AlterarVerificacaoPeritoCnpjJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjJobGroupName, VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjAtiva);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjJobGroupName, VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjAtiva);
    }

    public async Task AlterarVerificacaoPrevencaoTipo31Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31Ativa);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo34Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34Ativa);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34JobGroupName, VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34Ativa);
    }

    public async Task AlterarVerificacaoSucumbencial(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoSucumbencialJobGroupName, VerificacaoRequisicoesSettings.VerificacaoSucumbencialAtiva);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoSucumbencialJobGroupName, VerificacaoRequisicoesSettings.VerificacaoSucumbencialAtiva);
    }

    public async Task AlterarVerificacaoOrgaoPssJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoOrgaoPssJobGroupName, VerificacaoRequisicoesSettings.VerificacaoOrgaoPssAtiva);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoOrgaoPssJobGroupName, VerificacaoRequisicoesSettings.VerificacaoOrgaoPssAtiva);
    }

    public async Task AlterarVerificacaoNomeParteJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob(VerificacaoRequisicoesSettings.VerificacaoNomeParteJobGroupName, VerificacaoRequisicoesSettings.VerificacaoNomePartesAtiva);
        else
            await DesabilitarJob(VerificacaoRequisicoesSettings.VerificacaoNomeParteJobGroupName, VerificacaoRequisicoesSettings.VerificacaoNomePartesAtiva);
    }

    private async Task DesabilitarJob(string jobGroupName, string nomeConfiguracao)
    {
        await BackgroundJobsService.PausarJobsAsync(jobGroupName);
        await SettingManager.SetGlobalBoolAsync(nomeConfiguracao, false);

        if (DeveDesabilitarEnfileiraVerificacoesPeriodicoJob())
            await BackgroundJobsService.PausarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
    }

    private async Task HabilitarJob(string jobGroupName, string nomeConfiguracao)
    {
        if (!await BackgroundJobsService.IsJobPeriodicoHabilitadoAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobName))
        {
            var enfileiraConsultaControlePeriodicoJob = LazyServiceProvider.GetService<IEnfileiraVerificacoesPeriodicoJob>()!;
            enfileiraConsultaControlePeriodicoJob.Agendar();
            await BackgroundJobsService.RetomarJobsAsync(VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName);
        }

        await SettingManager.SetGlobalBoolAsync(nomeConfiguracao, true);
        await BackgroundJobsService.RetomarJobsAsync(jobGroupName);
    }

    private bool DeveDesabilitarEnfileiraVerificacoesPeriodicoJob()
    {
        return !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoCpfCnpjAtiva) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoAjuizamentoAtiva) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoOrgaoPssAtiva) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoAtiva) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjAtiva) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoSucumbencialAtiva) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoNomePartesAtiva) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoComplementarIgualAtiva) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualAtiva) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualAtiva) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo21Ativa) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo22Ativa) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo23Ativa) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo24Ativa) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31Ativa) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo32Ativa) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34Ativa) &&
               !IsConfiguracaoAtiva(VerificacaoRequisicoesSettings.VerificacaoSucumbencialAtiva);
    }
}