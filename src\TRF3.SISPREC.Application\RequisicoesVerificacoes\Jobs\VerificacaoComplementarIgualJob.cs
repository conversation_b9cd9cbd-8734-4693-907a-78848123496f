using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using TRF3.SISPREC.VerificacaoComplementarIgual;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoComplementarIgualJob : AsyncBackgroundJob<VerificacaoComplementarIgualJobArgs>, IVerificacaoComplementarIgualJob
{
    private readonly IVerificacaoComplementarIgualService _verificacaoComplementarIgualService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public VerificacaoComplementarIgualJob(IVerificacaoComplementarIgualService verificacaoComplementarIgualService, IUnitOfWorkManager unitOfWorkManager)
    {
        _verificacaoComplementarIgualService = verificacaoComplementarIgualService;
        _unitOfWorkManager = unitOfWorkManager;
    }

    public override async Task ExecuteAsync(VerificacaoComplementarIgualJobArgs args)
    {
        try
        {
            if (args.RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar VerificacaoComplementarIgualJob. RequisicaoVerificacaoId inválido: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
                return;
            }

            if (string.IsNullOrEmpty(args.NumeroProtocoloRequisicao))
            {
                Logger.LogError("Erro ao executar VerificacaoComplementarIgualJob. NumeroProtocoloRequisicao inválido: NumeroProtocoloRequisicao: {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
                return;
            }

            using IUnitOfWork uow = _unitOfWorkManager.Begin(false, true);
            await _verificacaoComplementarIgualService.VerificarComplementarAsync(args.RequisicaoVerificacaoId, args.NumeroProtocoloRequisicao);
            await uow.CompleteAsync();

        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoComplementarIgualJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
            throw;
        }
    }
}

public class VerificacaoComplementarIgualJobArgs : BaseBackgroundJobArgs
{
    public long RequisicaoVerificacaoId { get; set; }
    public string NumeroProtocoloRequisicao { get; set; }

    public override string JobGroupName => VerificacaoRequisicoesSettings.VerificacaoComplementarIgualJobGroupName;

    public VerificacaoComplementarIgualJobArgs(long requisicaoVerificacaoId, string numeroProtocoloRequisicao)
    {
        RequisicaoVerificacaoId = requisicaoVerificacaoId;
        NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
    }
}