@page
@using TRF3.SISPREC.Permissoes
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.Blocos
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@inject IAuthorizationService Authorization
@{
	PageLayout.Content.Title = "Blocos";
}

@section scripts
{
	<abp-script src="/Pages/Blocos/index.js" />
}

<abp-card>
	<abp-card-header>
		<abp-row class="justify-content-between align-items-center">
			<abp-column>
				<a abp-collapse-id="BlocosCollapse" class="text-secondary">Filtrar</a>
			</abp-column>
			<abp-column class="text-end">
				@if (await Authorization.IsGrantedAsync(SISPRECPermissoes.Blocos.Gravar))
				{
					<abp-button id="NewBlocoButton"
								text="Novo"
								icon="plus"
								button-type="Primary" />
				}
			</abp-column>
		</abp-row>
	</abp-card-header>
	<abp-card-body>
		<form asp-for="BlocosFilter" id="BlocosFilter">
			<abp-row>
				<abp-column>
					<abp-input asp-for="BlocosFilter.NumeroBloco" />
				</abp-column>
				<abp-column>
					<abp-date-picker asp-for="BlocosFilter.DataCriacaoInicio" />
				</abp-column>
				<abp-column>
					<abp-date-picker asp-for="BlocosFilter.DataCriacaoFim" />
				</abp-column>
				<abp-column>
					<abp-input asp-for="BlocosFilter.Usuario" />
				</abp-column>
				<abp-column>
					<abp-select asp-for="BlocosFilter.Status" />
				</abp-column>
				<abp-column>
					<abp-input asp-for="BlocosFilter.NumeroExpediente" />
				</abp-column>
				<abp-column>
					<input-requisicao-pesquisa asp-for="BlocosFilter.Requisicao" />
				</abp-column>
				<abp-column style="align-content:center; text-align: end;">
					<abp-button size="Small" button-type="Primary" id="btnPesquisar">
						Pesquisar
					</abp-button>
				</abp-column>
			</abp-row>
		</form>
		<abp-table striped-rows="true" id="BlocosTable" class="nowrap" />
	</abp-card-body>
</abp-card>
