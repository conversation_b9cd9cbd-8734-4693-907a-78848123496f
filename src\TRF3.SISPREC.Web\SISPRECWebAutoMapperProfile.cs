using AutoMapper;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.AcaoTipos.Dtos;
using TRF3.SISPREC.AcoesJustificativa.Dtos;
using TRF3.SISPREC.AdvogadosJudiciais.Dtos;
using TRF3.SISPREC.Agencias.Dtos;
using TRF3.SISPREC.Assuntos.Dtos;
using TRF3.SISPREC.Bancos.Dtos;
using TRF3.SISPREC.Enderecos.Dtos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.ModelosDocumentos.Dtos;
using TRF3.SISPREC.OcorrenciaMotivos.Dtos;
using TRF3.SISPREC.Pessoas.Dtos;
using TRF3.SISPREC.Pessoas.Dtos.EnderecosPessoas;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.Propostas.Dtos;
using TRF3.SISPREC.RequisicoesPlanosOrcamentos.Dtos;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesPropostas.Dtos;
using TRF3.SISPREC.RequisicoesProtocolos.Dtos;
using TRF3.SISPREC.SincronizacaoProgressos.Dtos;
using TRF3.SISPREC.SincronizacoesDominios.Dtos;
using TRF3.SISPREC.Unidades.Dtos;
using TRF3.SISPREC.UnidadesJudiciais.ContasUnidadesJudiciais.Dtos;
using TRF3.SISPREC.UnidadesJudiciais.Dtos;
using TRF3.SISPREC.UnidadesOrcamentarias.Dtos;
using TRF3.SISPREC.ViewAuditoriaEntidades.Dtos;
using TRF3.SISPREC.ViewAuditoriaPropriedades.Dtos;
using TRF3.SISPREC.ViewControles.Dtos;
using TRF3.SISPREC.ViewProcessos.Dtos;
using TRF3.SISPREC.Web.Pages.AcoesJustificativa.ViewModels;
using TRF3.SISPREC.Web.Pages.AdvogadosJudiciais.ViewModels;
using TRF3.SISPREC.Web.Pages.Agencias.Agencia.ViewModels;
using TRF3.SISPREC.Web.Pages.Agencias.ViewModels;
using TRF3.SISPREC.Web.Pages.Assuntos.ViewModels;
using TRF3.SISPREC.Web.Pages.Bancos.Banco.ViewModels;
using TRF3.SISPREC.Web.Pages.ModelosDocumentos.ViewModels;
using TRF3.SISPREC.Web.Pages.OcorrenciaMotivos.ViewModels;
using TRF3.SISPREC.Web.Pages.Pessoas.ViewModels;
using TRF3.SISPREC.Web.Pages.Pessoas.ViewModels.EnderecosPessoas;
using TRF3.SISPREC.Web.Pages.Propostas.ViewModels;
using TRF3.SISPREC.Web.Pages.RequisicoesPlanosOrcamentos.ViewModels;
using TRF3.SISPREC.Web.Pages.RequisicoesProtocolos.ViewModels;
using TRF3.SISPREC.Web.Pages.SincronizacoesDominios.SincronizacaoDominio.ViewModels;
using TRF3.SISPREC.Web.Pages.Unidades.ViewModels;
using TRF3.SISPREC.Web.Pages.UnidadesJudiciais.ViewModels;
using TRF3.SISPREC.Web.Pages.ViewAuditoriaEntidades.ViewAuditoriaEntidade.ViewModels;
using TRF3.SISPREC.Web.Pages.ViewControles.ViewControle.ViewModels;
using TRF3.SISPREC.Web.Pages.ViewProcessos.ViewProcesso.ViewModels;
using TRF3.SISPREC.Web.Pages.ViewRequisicoes.ViewModels;
using TRF3.SISPREC.Web.Pages.TipoIndicadorEconomicos.ViewModels;
using TRF3.SISPREC.Web.Pages.IndicadorEconomicos.ViewModels;
using TRF3.SISPREC.Peritos.Dtos;
using TRF3.SISPREC.Web.Pages.Peritos.ViewModels;
using TRF3.SISPREC.IndicadorEconomicoTipos.Dtos;
using TRF3.SISPREC.IndicadorEconomicos.Dtos;
using TRF3.SISPREC.Web.Pages.ConsultarJustificativa.ViewModels;
using TRF3.SISPREC.RequisicaoJustificativas.Dtos;
using TRF3.SISPREC.AnalisePrevencoes.Dtos;
using TRF3.SISPREC.Web.Pages.AnalisePrevencoes.ViewModels;
using TRF3.SISPREC.Web.Pages.Setor.ViewModels;
using TRF3.SISPREC.Setores.Dtos;
using TRF3.SISPREC.MotivosExpedientesAdministrativos.Dtos;
using TRF3.SISPREC.MotivosExpedientesAdministrativos;
using TRF3.SISPREC.Web.Pages.MotivosExpedientesAdministrativos.ViewModels;
using TRF3.SISPREC.BlocosSisprec.Dtos;
using TRF3.SISPREC.BlocosSisprec;

namespace TRF3.SISPREC.Web;

[ExcludeFromCodeCoverage]
public class SISPRECWebAutoMapperProfile : Profile
{
    public SISPRECWebAutoMapperProfile()
    {
        //Define your AutoMapper configuration here for the Web project.
        CreateMap<SincronizacaoDominioDto, DetalheSincronizacaoDominioViewModel>();
        CreateMap<SincronizacaoProgressoDto, SincronizacaoProgressoViewModel>();
        CreateMap<DetalheSincronizacaoDominioViewModel, DetalheSincronizacaoDominioDto>();
        CreateMap<ViewControleDto, UploadMDBViewModel>();
        CreateMap<ViewProcessoDto, ViewProcessoViewModel>();
        CreateMap<AcaoTipoDto, AcaoJustificativaViewModel>()
             .ReverseMap();
        CreateMap<AcaoJustificativaDto, CreateEditAcaoJustificativaViewModel>()
            .ForMember(dest => dest.AcaoTipo, opt => opt.MapFrom(src => src.AcaoTipo))
            .ReverseMap();
        CreateMap<CreateEditAcaoJustificativaViewModel, CreateUpdateAcaoJustificativaDto>();
        CreateMap<AcaoJustificativaDto, DetalheAcaoJustificativaViewModel>()
            .ForMember(dest => dest.AcaoJustificativa, opt => opt.MapFrom(src => src.AcaoTipo))
            .ReverseMap();
        CreateMap<DetalheAcaoJustificativaViewModel, CreateUpdateAcaoJustificativaDto>()
            .ForMember(dest => dest.Acao, opt => opt.MapFrom(src => src.AcaoJustificativa))
            .ReverseMap();
        CreateMap<ViewAuditoriaPropriedadeDto, DetalheViewAuditoriaPropriedadeViewModel>();
        CreateMap<ViewAuditoriaEntidadeDto, DetalheViewAuditoriaEntidadeViewModel>();
        CreateMap<DetalheUnidadeJudicialViewModel, CreateUpdateUnidadeJudicialDto>();

        #region Assunto

        CreateMap<CreateUpdateAssuntoDto, CreateEditAssuntoViewModel>()
            .ForMember(dest => dest.AssuntoAuxiliarViewModel, opt => opt.MapFrom(src => src.AssuntoAuxiliarDto))
            .ReverseMap();
        CreateMap<AssuntoDto, CreateEditAssuntoViewModel>()
            .ForMember(dest => dest.AssuntoAuxiliarViewModel, opt => opt.MapFrom(src => src.AssuntoAuxiliarDto))
            .ReverseMap();
        CreateMap<AssuntoDto, DetalheAssuntoViewModel>()
            .ForMember(dest => dest.AssuntoAuxiliarViewModel, opt => opt.MapFrom(src => src.AssuntoAuxiliarDto))
            .ReverseMap();
        CreateMap<AssuntoAuxiliarViewModel, AssuntoAuxiliarDto>().ReverseMap();

        #endregion

        #region UnidadeJudicial

        CreateMap<CreateUpdateUnidadeJudicialDto, CreateEditUnidadeJudicialViewModel>()
            .ForMember(x => x.UnidadeJudicialOrigem, opt => opt.MapFrom(src => src.UnidadeJudicialOrigem))
            .ReverseMap();

        CreateMap<UnidadeJudicialDto, CreateEditUnidadeJudicialViewModel>()
            .ForMember(dest => dest.UnidadeJudicialOrigem, opt => opt.MapFrom(src => src.UnidadeJudicialOrigem))
            .ReverseMap();

        CreateMap<UnidadeJudicialDto, DetalheUnidadeJudicialViewModel>()
            .ForMember(dest => dest.UnidadeJudicialOrigem, opt => opt.MapFrom(src => src.UnidadeJudicialOrigem))
            .ReverseMap();

        CreateMap<UnidadeJudicialOrigemViewModel, UnidadeJudicialOrigemDto>().ReverseMap();
        CreateMap<CreateContaUnidadeJudicialViewModel, CreateContaUnidadeJudicialDto>().ReverseMap();
        CreateMap<EditContaUnidadeJudicialViewModel, ContaUnidadeJudicialDto>().ReverseMap();
        CreateMap<EditContaUnidadeJudicialViewModel, UpdateContaUnidadeJudicialDto>().ReverseMap();

        #endregion

        #region Unidade Orcamentaria

        CreateMap<DetalheUnidadeOrcamentariaViewModel, CreateUpdateUnidadeOrcamentariaDto>()
            .ReverseMap();
        CreateMap<UnidadeOrcamentariaDto, DetalheUnidadeOrcamentariaViewModel>()
            .ForMember(dest => dest.UnidadeDto, opt => opt.MapFrom(src => src.UnidadeDto))
            .ReverseMap();

        #endregion

        #region Unidade Auxiliar

        CreateMap<UnidadeViewModel, UnidadeDto>();
        CreateMap<UnidadeDto, UnidadeViewModel>();

        #endregion

        CreateMap<RequisicaoProtocoloDto, CreateEditRequisicaoProtocoloViewModel>();
        CreateMap<CreateEditRequisicaoProtocoloViewModel, CreateUpdateRequisicaoProtocoloDto>();
        CreateMap<RequisicaoProtocoloDto, DetalheRequisicaoProtocoloViewModel>();
        CreateMap<DetalheRequisicaoProtocoloViewModel, CreateUpdateRequisicaoProtocoloDto>();

        #region pessoa
        CreateMap<PessoaDto, CreateEditPessoaViewModel>();
        CreateMap<CreateEditPessoaViewModel, CreateUpdatePessoaDto>();
        CreateMap<EnderecoPessoaDto, EditEnderecoPessoaViewModel>();
        CreateMap<EditEnderecoPessoaViewModel, EnderecoPessoaDto>();
        CreateMap<PessoaDto, DetalhePessoaViewModel>();
        CreateMap<PessoaDto, DetalhePessoaViewModel>();
        CreateMap<DetalhePessoaViewModel, CreateUpdatePessoaDto>();
        CreateMap<EnderecoPessoaDto, CreateEditEnderecoPessoaViewModel>();
        CreateMap<CreateEditEnderecoPessoaViewModel, EnderecoPessoaDto>();

        CreateMap<CreateEditEnderecoPessoaViewModel, CreateUpdateEnderecoPessoaDto>();
        CreateMap<EnderecoPessoaDto, DetalheEnderecoPessoaViewModel>()
            .ForMember(dest => dest.PessoaId, opt => opt.MapFrom(src => src.PessoaId));
        CreateMap<EnderecoPessoaDto, EditEnderecoPessoaViewModel>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<EditEnderecoPessoaViewModel, EnderecoPessoaDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<DetalheEnderecoPessoaViewModel, CreateUpdateEnderecoPessoaDto>();
        CreateMap<CreateEditPessoaViewModel, MunicipioDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.Endereco.MunicipioId));
        CreateMap<EditEnderecoPessoaViewModel, MunicipioDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<MunicipioDto, EnderecoPessoaDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<MunicipioDto, CreateEditEnderecoPessoaViewModel>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<EditEnderecoPessoaViewModel, MunicipioDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<CreateEditEnderecoPessoaViewModel, MunicipioDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<EnderecoPessoaDto, CreateEditPessoaViewModel>()
          .ForMember(dest => dest.Endereco, opt => opt.MapFrom(src => src.Municipio));
        CreateMap<CreateEditPessoaViewModel, CreateUpdatePessoaDto>()
          .ForMember(dest => dest.Endereco, opt => opt.MapFrom(src => src.Endereco.Municipio));


        CreateMap<CreateEditEnderecoPessoaViewModel, CreateUpdateEnderecoPessoaDto>();
        CreateMap<CreateEditEnderecoPessoaViewModel, CreateUpdateEnderecoPessoaDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<MunicipioDto, CreateUpdateEnderecoPessoaDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<CreateUpdatePessoaDto, EnderecoPessoaDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.Endereco.MunicipioId));
        CreateMap<EnderecoPessoaDto, MunicipioDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<MunicipioDto, EnderecoPessoaDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));
        CreateMap<CreateEditPessoaViewModel, CreateUpdatePessoaDto>()
            .ForMember(dest => dest.Endereco, opt => opt.MapFrom(src => src.Endereco));
        CreateMap<EditEnderecoPessoaViewModel, CreateUpdateEnderecoPessoaDto>()
            .ForMember(dest => dest.MunicipioDto, opt => opt.MapFrom(src => src.Municipio));
        CreateMap<CreateUpdateEnderecoPessoaDto, EditEnderecoPessoaViewModel>()
            .ForMember(dest => dest.Municipio, opt => opt.MapFrom(src => src.MunicipioDto));





        #endregion

        CreateMap<AdvogadoJudicialDto, CreateEditAdvogadoJudicialViewModel>();
        CreateMap<CreateEditAdvogadoJudicialViewModel, CreateUpdateAdvogadoJudicialDto>();
        CreateMap<AdvogadoJudicialDto, DetalheAdvogadoJudicialViewModel>();
        CreateMap<DetalheAdvogadoJudicialViewModel, CreateUpdateAdvogadoJudicialDto>();

        CreateMap<BancoDto, CreateBancoViewModel>();
        CreateMap<BancoDto, UpdateBancoViewModel>();
        CreateMap<CreateBancoViewModel, CreateBancoDto>();
        CreateMap<UpdateBancoViewModel, UpdateBancoDto>();
        CreateMap<BancoDto, DetalheBancoViewModel>();
        CreateMap<DetalheBancoViewModel, CreateBancoDto>();

        CreateMap<AgenciaDto, CreateAgenciaViewModel>();
        CreateMap<AgenciaDto, UpdateAgenciaViewModel>();
        CreateMap<CreateAgenciaViewModel, CreateAgenciaDto>();
        CreateMap<UpdateAgenciaViewModel, UpdateAgenciaDto>();
        CreateMap<AgenciaDto, DetalheAgenciaViewModel>();
        CreateMap<DetalheAgenciaViewModel, CreateAgenciaDto>();

        CreateMap<MunicipioDto, MunicipioViewModel>();
        CreateMap<Proposta, PropostaDto>()
            .ForMember(dest => dest.SituacaoProposta, opt => opt.MapFrom(src => src.SituacaoProposta.GetEnumDescription()));
        CreateMap<PropostaDto, DetalhePropostaViewModel>();
        CreateMap<DetalhePropostaViewModel, CreateUpdatePropostaDto>();
        CreateMap<RequisicaoProposta, RequisicaoPropostaDto>()
            .ForMember(dest => dest.SituacaoRequisicaoProposta, opt => opt.MapFrom(src => src.SituacaoRequisicaoProposta.GetEnumDescription()));

        CreateMap<OcorrenciaMotivoDto, CreateEditOcorrenciaMotivoViewModel>();
        CreateMap<CreateEditOcorrenciaMotivoViewModel, CreateUpdateOcorrenciaMotivoDto>();

        CreateMap<OcorrenciaMotivoDto, EditOcorrenciaMotivoViewModel>();
        CreateMap<EditOcorrenciaMotivoViewModel, CreateUpdateOcorrenciaMotivoDto>();

        CreateMap<OcorrenciaMotivoDto, CreateOcorrenciaMotivoViewModel>();
        CreateMap<CreateOcorrenciaMotivoViewModel, CreateUpdateOcorrenciaMotivoDto>();


        CreateMap<OcorrenciaMotivoDto, DetalheOcorrenciaMotivoViewModel>();
        CreateMap<DetalheOcorrenciaMotivoViewModel, CreateUpdateOcorrenciaMotivoDto>();


        CreateMap<MotivoExpedienteAdministrativoDto, CreateEditMotivoExpedienteAdministrativoViewModel>();
        CreateMap<CreateEditMotivoExpedienteAdministrativoViewModel, CreateUpdateMotivoExpedienteAdministrativoDto>();

        CreateMap<MotivoExpedienteAdministrativoDto, EditMotivoExpedienteAdministrativoViewModel>();
        CreateMap<EditMotivoExpedienteAdministrativoViewModel, CreateUpdateMotivoExpedienteAdministrativoDto>();

        CreateMap<MotivoExpedienteAdministrativoDto, CreateMotivoExpedienteAdministrativoViewModel>();
        CreateMap<CreateMotivoExpedienteAdministrativoViewModel, CreateUpdateMotivoExpedienteAdministrativoDto>();

        CreateMap<MotivoExpedienteAdministrativoDto, DetalheMotivoExpedienteAdministrativoViewModel>();
        CreateMap<DetalheMotivoExpedienteAdministrativoViewModel, CreateUpdateMotivoExpedienteAdministrativoDto>();

        CreateMap<MotivoExpedienteAdministrativo, EditMotivoExpedienteAdministrativoViewModel>();

        CreateMap<CreateUpdateMotivoExpedienteAdministrativoDto, MotivoExpedienteAdministrativo>();

        #region ViewRequisicao

        CreateMap<RequisicaoPropostaDetalheViewModel, RequisicaoPropostaDto>()
            .ForPath(dest => dest.Proposta.TipoProcedimentoId, opt => opt.MapFrom(src => src.TipoProcedimentoId))
            .ForPath(dest => dest.Proposta.AnoProposta, opt => opt.MapFrom(src => src.AnoPropos))
            .ForPath(dest => dest.Proposta.MesProposta, opt => opt.MapFrom(src => src.MesPropos))
            .ForMember(dest => dest.NumeroProtocoloRequisicao, opt => opt.MapFrom(src => src.NumeroProtocoloRequisicao))
            .ForPath(dest => dest.RequisicaoProtocolo.NumeroProtocoloRequisicaoUnica, opt => opt.MapFrom(src => src.NumeroProtocoloRequisicaoUnica))
            .ForPath(dest => dest.RequisicaoProtocolo.NumeroOficioRequisitorio, opt => opt.MapFrom(src => src.NumeroOficioRequisitorio))
            .ForPath(dest => dest.RequisicaoProtocolo.DataHoraProtocoloRequisicao, opt => opt.MapFrom(src => src.DataHoraProtocoloRequisicao))
            .ForPath(dest => dest.RequisicaoProtocolo.UnidadeJudicialDto.CodigoSiafi, opt => opt.MapFrom(src => src.CodigoSiafi))
            .ForPath(dest => dest.RequisicaoProtocolo.UnidadeJudicialDto.UnidadeJudicialOrigem.CodigoEspecicacaoTipoJuizo, opt => opt.MapFrom(src => src.CodigoEspecicacaoTipoJuizo))
            .ForPath(dest => dest.RequisicaoProtocolo.ExecucaoFiscal, opt => opt.MapFrom(src => src.ExecucaoFiscal))
            .ForPath(dest => dest.RequisicaoProtocolo.TipoHonorario, opt => opt.MapFrom(src => src.TipoHonorario))
            .ForPath(dest => dest.RequisicaoProtocolo.RenunciaValorLimite, opt => opt.MapFrom(src => src.RenunciaValorLimite))
            .ForPath(dest => dest.RequisicaoProtocolo.AssuntoDto.CodigoCJF, opt => opt.MapFrom(src => src.CodigoCJF))
            .ForPath(dest => dest.RequisicaoProtocolo.DataTransitoJulgadoFase, opt => opt.MapFrom(src => src.DataTransitoJulgadoFase))
            .ForPath(dest => dest.RequisicaoProtocolo.DataTransitoJulgadoEmbargos, opt => opt.MapFrom(src => src.DataTransitoJulgadoEmbargo))
            .ForPath(dest => dest.RequisicaoProtocolo.TipoRequisicao, opt => opt.MapFrom(src => src.TipoRequisicao))
            .ForPath(dest => dest.RequisicaoProtocolo.NaturezaCredito, opt => opt.MapFrom(src => src.NaturezaCredito))
            .ForPath(dest => dest.RequisicaoProtocolo.BloqueioDepositoJudicial, opt => opt.MapFrom(src => src.BloqueioDepositoJudicial))
            .ForPath(dest => dest.RequisicaoProtocolo.LevantamentoOrdemJuizo, opt => opt.MapFrom(src => src.LevantamentoOrdemJuizo))
            .ForPath(dest => dest.RequisicaoProtocolo.SituacaoRequisicaoId, opt => opt.MapFrom(src => src.SituacaoRequisiacoId))
            .ForPath(dest => dest.RequisicaoProtocolo.SituacaoRequisicaoProtocoloDto.DescricaoSituacao, opt => opt.MapFrom(src => src.DescricaoSituacaoProtocolo))
            .ForPath(dest => dest.RequisicaoProtocolo.IndicadorEstornoRequisicao, opt => opt.MapFrom(src => src.DescricaoSituacaoProtocolo))
            .ForPath(dest => dest.RequisicaoProtocolo.AssuntoDto.DescricaoCJF, opt => opt.MapFrom(src => src.NomeCJF))
            .ForPath(dest => dest.RequisicaoProtocolo.DesapropriacaoUnicoImovel, opt => opt.MapFrom(src => src.DesapropriacaoUnicoImovel))
            .ForPath(dest => dest.RequisicaoProtocolo.Selic, opt => opt.MapFrom(src => src.Selic))
            .ForPath(dest => dest.RequisicaoProtocolo.ValorCompensacao, opt => opt.MapFrom(src => src.ValorCompensacao))
            .ForPath(dest => dest.RequisicaoProtocolo.ValorCompensacaoAtualizado, opt => opt.MapFrom(src => src.ValorCompensacaoAtualizado))
            .ForPath(dest => dest.RequisicaoProtocolo.ValorRequisicaoPrincipal, opt => opt.MapFrom(src => src.ValorRequisicaoPrincipal))
            .ForPath(dest => dest.RequisicaoProtocolo.ValorRequisicaoJuros, opt => opt.MapFrom(src => src.ValorRequisicaoJuros))
            .ForPath(dest => dest.RequisicaoProtocolo.ValorRequisicao, opt => opt.MapFrom(src => src.ValorRequisicao))
            .ForPath(dest => dest.RequisicaoProtocolo.DataContaLiquidacao, opt => opt.MapFrom(src => src.DataContaLiquidacao))
            .ForPath(dest => dest.RequisicaoProtocolo.ValorContaPrincipal, opt => opt.MapFrom(src => src.ValorContaPrincipal))
            .ForPath(dest => dest.RequisicaoProtocolo.ValorContaJuros, opt => opt.MapFrom(src => src.ValorContaJuros))
            .ForPath(dest => dest.RequisicaoProtocolo.ValorConta, opt => opt.MapFrom(src => src.ValorConta))
            .ForPath(dest => dest.RequisicaoProtocolo.DataConta, opt => opt.MapFrom(src => src.DataConta))
            .ForPath(dest => dest.RequisicaoProtocolo.ValorTotalReferencia, opt => opt.MapFrom(src => src.ValorTotalReferencia))
            .ForPath(dest => dest.RequisicaoProtocolo.NomeMagistrado, opt => opt.MapFrom(src => src.NomeMagistrado))
            .ForPath(dest => dest.RequisicaoProtocolo.DesignacaoMagistrado, opt => opt.MapFrom(src => src.DesignacaoMagistrado))
            .ForPath(dest => dest.RequisicaoProtocolo.IndicadorInclusaoRequisicao, opt => opt.MapFrom(src => src.IndicadorInclusaoRequisicao)).ReverseMap();

        CreateMap<DetalhesRequisicaoOriginariosViewModel, RequisicaoPropostaDto>()
            .ForMember(dest => dest.NumeroProtocoloRequisicao, opt => opt.MapFrom(src => src.NumProtocRequis))
            .ForPath(dest => dest.RequisicaoProtocolo.UnidadeJudicialDto.Seq_Unidad_Judici, opt => opt.MapFrom(src => src.CodPessoaJuizo))
            .ForPath(dest => dest.RequisicaoProtocolo.UnidadeJudicialDto.UnidadeJudicialOrigem.Email, opt => opt.MapFrom(src => src.Email))
            .ForPath(dest => dest.RequisicaoProtocolo.UnidadeJudicialDto.CodigoSiafi, opt => opt.MapFrom(src => src.CodSiafiUnidad))
            .ForPath(dest => dest.RequisicaoProtocolo.UnidadeJudicialDto.Descricao, opt => opt.MapFrom(src => src.Juizo)).ReverseMap();

        CreateMap<DetalhesRequisicaoRequeridoCompensacaoViewModel, DetalhesRequisicaoRequeridoCompensacaoDto>().ReverseMap();
        CreateMap<DetalhesRequisicaoIrPssViewModel, DetalhesRequisicaoIrPssDto>().ReverseMap();
        CreateMap<DetalhesRequisicaoRequerenteHonorariosViewModel, DetalhesRequisicaoRequerenteHonorariosDto>().ReverseMap();
        CreateMap<DetalhesRequisicaoAutorExpedienteViewModel, DetalhesRequisicaoAutorExpedienteDto>().ReverseMap();
        CreateMap<DetalhesRequisicaoRequerente, DetalhesRequisicaoRequerenteDto>();
        CreateMap<DetalhesRequisicaoRequerenteDto, DetalhesRequisicaoRequerenteViewModel>();
        CreateMap<DetalhesRequisicaoOriginarios, DetalhesRequisicaoOriginariosDto>();
        CreateMap<DetalhesRequisicaoAutorExpedienteListagem, DetalhesRequisicaoAutorExpedienteListagemDto>();
        CreateMap<DetalhesRequisicaoPrincipalDto, RequisicaoPropostaDetalheViewModel>().ReverseMap();




        #endregion

        CreateMap<ModeloDocumentoDto, CreateEditModeloDocumentoViewModel>();
        CreateMap<CreateModeloDocumentoViewModel, CreateUpdateModeloDocumentoDto>();
        CreateMap<CreateEditModeloDocumentoViewModel, CreateUpdateModeloDocumentoDto>();
        CreateMap<RequisicaoPlanoOrcamentoDto, CreateEditRequisicaoPlanoOrcamentoViewModel>();
        CreateMap<CreateEditRequisicaoPlanoOrcamentoViewModel, CreateUpdateRequisicaoPlanoOrcamentoDto>();
        CreateMap<RequisicaoPlanoOrcamentoDto, DetalheRequisicaoPlanoOrcamentoViewModel>();
        CreateMap<DetalheRequisicaoPlanoOrcamentoViewModel, CreateUpdateRequisicaoPlanoOrcamentoDto>();
        CreateMap<IndicadorEconomicoTipoDto, CreateEditIndicadorEconomicoTipoViewModel>();
        CreateMap<CreateEditIndicadorEconomicoTipoViewModel, CreateUpdateIndicadorEconomicoTipoDto>();
        CreateMap<IndicadorEconomicoTipoDto, DetalheIndicadorEconomicoTipoViewModel>();
        CreateMap<IndicadorEconomicoDto, DetalheIndicadorEconomicoViewModel>();
        CreateMap<DetalheIndicadorEconomicoTipoViewModel, CreateUpdateIndicadorEconomicoTipoDto>();
        CreateMap<PeritoDto, CreateEditPeritoViewModel>();
        CreateMap<CreateEditPeritoViewModel, CreateUpdatePeritoDto>();
        CreateMap<CreateUpdateRequisicaoJustificativaViewModel, CreateUpdateJustificativaComplementoDto>()
            .ForMember(dest => dest.TipoAnalise, opt => opt.Ignore());
        CreateMap<CreateObservacaoGeracaoEspelhoViewModel, ObservacaoGeracaoEspelhoDto>();

        CreateMap<CreateEditSetorViewModel, CreateUpdateSetorDto>().ReverseMap();
        CreateMap<SetorDto, DetalheSetorViewModel>().ReverseMap();
        CreateMap<SetorDto, CreateEditSetorViewModel>().ReverseMap();

        CreateMap<BlocoSisprec, BlocoSisprecDto>().ReverseMap();
        CreateMap<CreateUpdateModeloBlocoSisprecDto, BlocoSisprec>().ReverseMap();

    }
}
