$(function () {

    $("#BlocosFilter :input").on('input', function () {
        dataTable.ajax.reload();
    });


    const getFilter = function () {
        const input = {};
        $("#BlocosFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/BlocosFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.blocosSisprec.blocoSisprec;

    const dataTable = $('#BlocosTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: false,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: false,
        scrollCollapse: true,
        order: [[1, "desc"]],
        ajax: abp.libs.datatables.createAjax(service.getList, getFilter),
        columnDefs: [
            {
                rowAction: {
                    items:
                        [
                            {
                                text: "Gerar Documentos",
                                action: function (data) {
                                    detalheModal.open({ id: data.record.blocosId });
                                }
                            },
                            {
                                text: "Enviar para Conferência",
                                action: function (data) {
                                    editModal.open({ id: data.record.blocosId });
                                }
                            }
                        ]
                }
            },
            {
                title: "Nº BLOCO",
                data: "blocoSisprecId"
            },
            {
                title: "DATA",
                data: "dataCriacao",
                render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss')
            },
            {
                title: "USUÁRIO",
                data: "nomeUsuario"
            },
            {
                title: "Status",
                data: "statusBloco"
            }
        ]
    }));

    $('#NewBlocosButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });
});
