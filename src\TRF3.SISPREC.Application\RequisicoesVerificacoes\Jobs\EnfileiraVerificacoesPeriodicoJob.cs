using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundJobs;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.SettingManagement;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[DisallowConcurrentExecution]
[ExposeServices(typeof(IEnfileiraVerificacoesPeriodicoJob))]
public class EnfileiraVerificacoesPeriodicoJob : SchedulingBackroundJob<EnfileiraVerificacoesPeriodicoJob>, IEnfileiraVerificacoesPeriodicoJob
{
    private readonly IBackgroundJobManager _backgroundJobManager;
    private readonly IRequisicaoVerificacaoRepository _requisicaoVerificacaoRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly ISettingManager _settingManager;

    public override string JobName => VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobName;

    public override string JobGroupName => VerificacaoRequisicoesSettings.EnfileiraVerificacoesPeriodicoJobGroupName;
    protected override int IntervalInSeconds => 5 * 60;//Executar a cada 5 minutos

    public EnfileiraVerificacoesPeriodicoJob(
        IGetLoggerService getLoggerService,
        IScheduler scheduler,
        IBackgroundJobManager backgroundJobManager,
        IRequisicaoVerificacaoRepository requisicaoVerificacaoRepository,
        IUnitOfWorkManager unitOfWorkManager,
        ISettingManager settingManager) : base(getLoggerService, scheduler)
    {
        _backgroundJobManager = backgroundJobManager;
        _requisicaoVerificacaoRepository = requisicaoVerificacaoRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _settingManager = settingManager;
    }

    public async override Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            Logger.LogInformation(">>>> Executando EnfileiraVerificacoesPeriodicoJob");
            using (var uow = _unitOfWorkManager.Begin(true, true))
            {
                var verificacoesNaoExecutadas = await (await _requisicaoVerificacaoRepository.GetQueryableAsync())
                                                .Include(x => x.VerificacaoTipo)
                                                .Where(x => !x.Executado)
                                                .AsNoTracking()
                                                .ToListAsync();

                foreach (var verificacoesNaoExecutada in verificacoesNaoExecutadas)
                {
                    context.CancellationToken.ThrowIfCancellationRequested();

                    if (IsConfiguracaoVerificacaoAtiva(verificacoesNaoExecutada.VerificacaoTipoId))
                    {
                        await _backgroundJobManager.EnqueueAsync(ObterJobArgs(verificacoesNaoExecutada));
                    }
                }

                await uow.CompleteAsync();
            }
        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "EnfileiraVerificacoesPeriodicoJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar EnfileiraVerificacoesPeriodicoJob.");
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }

    private bool IsConfiguracaoVerificacaoAtiva(int verificacaoTipoId)
    {
        EVerificacaoRequisicaoTipo verificacaoRequisicaoTipo = EVerificacaoRequisicaoTipoHelper.Parse(verificacaoTipoId);
        return _settingManager.GetGlobalBool(VerificacaoRequisicoesSettings.ObterNomeConfiguracaoPorTipoVerificacao(verificacaoRequisicaoTipo));
    }

    private dynamic ObterJobArgs(RequisicaoVerificacao verificacao)
    {
        Type classeJobArgs = ObterClasseJobArgs(verificacao.VerificacaoTipoId);
        object[] paramsConstrutorJobArgs = ObterParamsConstrutorJobArgs(verificacao);

        return Activator.CreateInstance(classeJobArgs, paramsConstrutorJobArgs)!;
    }

    [ExcludeFromCodeCoverage]
    private object[] ObterParamsConstrutorJobArgs(RequisicaoVerificacao verificacao)
    {
        return EVerificacaoRequisicaoTipoHelper.Parse(verificacao.VerificacaoTipoId) switch
        {
            EVerificacaoRequisicaoTipo.VERIFICACAO_CPF_CNPJ => [verificacao.RequisicaoVerificacaoId, verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_AJUIZAMENTO => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_COMPLEMENTAR_IGUAL => [verificacao.RequisicaoVerificacaoId, verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_SUPLEMENTAR_IGUAL => [verificacao.RequisicaoVerificacaoId, verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_INCONTROVERSO_MENOR_IGUAL => [verificacao.RequisicaoVerificacaoId, verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_PERITO_ADVOGADO => [verificacao.RequisicaoVerificacaoId, verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_PERITO_CNPJ => [verificacao.RequisicaoVerificacaoId, verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_21 => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_22 => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_23 => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_24 => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_31 => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_32 => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_34 => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_35 => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_SUCUMBENCIAL => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_ORGAO_PSS => [verificacao.NumeroProtocoloRequisicaoId],
            EVerificacaoRequisicaoTipo.VERIFICACAO_NOME_PARTES => [verificacao.NumeroProtocoloRequisicaoId],
            _ => throw new ArgumentException($"Valor inválido para VerificacaoTipoId: {verificacao.VerificacaoTipoId}")
        };
    }

    [ExcludeFromCodeCoverage]
    private Type ObterClasseJobArgs(int verificacaoTipoId)
    {
        return EVerificacaoRequisicaoTipoHelper.Parse(verificacaoTipoId) switch
        {
            EVerificacaoRequisicaoTipo.VERIFICACAO_CPF_CNPJ => typeof(ConsultaCnpjCpfArgs),
            EVerificacaoRequisicaoTipo.VERIFICACAO_AJUIZAMENTO => typeof(VerificacaoAjuizamentoArgs),
            EVerificacaoRequisicaoTipo.VERIFICACAO_COMPLEMENTAR_IGUAL => typeof(VerificacaoComplementarIgualJobArgs),
            EVerificacaoRequisicaoTipo.VERIFICACAO_SUPLEMENTAR_IGUAL => typeof(VerificacaoSuplementarIgualJobArgs),
            EVerificacaoRequisicaoTipo.VERIFICACAO_INCONTROVERSO_MENOR_IGUAL => typeof(VerificacaoIncontroversoMenorIgualJobArgs),
            EVerificacaoRequisicaoTipo.VERIFICACAO_PERITO_ADVOGADO => typeof(VerificacaoPeritoAdvogadoJobArgs),
            EVerificacaoRequisicaoTipo.VERIFICACAO_PERITO_CNPJ => typeof(VerificacaoPeritoCnpjJobArgs),
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_21 => typeof(VerificacaoPrevencaoTipo21Args),
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_22 => typeof(VerificacaoPrevencaoTipo22Args),
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_23 => typeof(VerificacaoPrevencaoTipo23Args),
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_24 => typeof(VerificacaoPrevencaoTipo24Args),
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_31 => typeof(VerificacaoPrevencaoTipo31Args),
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_32 => typeof(VerificacaoPrevencaoTipo32Args),
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_34 => typeof(VerificacaoPrevencaoTipo34Args),
            EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_35 => typeof(VerificacaoPrevencaoTipo35Args),
            EVerificacaoRequisicaoTipo.VERIFICACAO_SUCUMBENCIAL => typeof(VerificacaoSucumbencialArgs),
            EVerificacaoRequisicaoTipo.VERIFICACAO_ORGAO_PSS => typeof(VerificacaoOrgaoPssArgs),
            EVerificacaoRequisicaoTipo.VERIFICACAO_NOME_PARTES => typeof(VerificacaoNomeParteArgs),
            _ => throw new ArgumentException($"Valor inválido para VerificacaoTipoId: {verificacaoTipoId}")
        };
    }
}