using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.ModelosDocumentos;
using TRF3.SISPREC.ModelosDocumentos.Dtos;
using TRF3.SISPREC.Setores;
using TRF3.SISPREC.Setores.Dtos;
using TRF3.SISPREC.Web.Pages.ModelosDocumentos.ViewModels;
using Volo.Abp;

namespace TRF3.SISPREC.Web.Pages.ModelosDocumentos
{
    public class CreateModel(ISetorAppService setorService, IModeloDocumentoAppService modeloDocumentoAppService) : SISPRECPageModel
    {
        [BindProperty]
        public CreateModeloDocumentoViewModel ViewModel { get; set; } = new();

        public virtual async Task OnGetAsync()
        {
            var setores = await setorService.GetListAsync(new SetorGetListInput());

            ViewModel.SetorLookupList.AddRange(setores.Items.Select(x => new SelectListItem
            {
                Value = x.SetorId.ToString(),
                Text = x.Sigla
            }));
            ViewModel.SetorLookupList.AddFirst(new SelectListItem());
            await Task.CompletedTask;
        }

        [ExcludeFromCodeCoverage]
        public virtual async Task<IActionResult> OnPostAsync()
        {
            try
            {
                // Validação do ModelState
                if (!ModelState.IsValid)
                {
                    // Recarrega a lista de setores em caso de erro
                    var setores = await setorService.GetListAsync(new SetorGetListInput());
                    ViewModel.SetorLookupList.Clear();
                    ViewModel.SetorLookupList.AddRange(setores.Items.Select(x => new SelectListItem
                    {
                        Value = x.SetorId.ToString(),
                        Text = x.Sigla
                    }));
                    ViewModel.SetorLookupList.AddFirst(new SelectListItem());

                    return new JsonResult(new { success = false, errors = GetModelStateErrors() });
                }

                // Mapeia o ViewModel para o DTO
                var dto = new CreateUpdateModeloDocumentoDto
                {
                    NomeModelo = ViewModel.NomeModelo,
                    SetorId = ViewModel.SetorId.Value,
                    TextoDocumento = ViewModel.TextoDocumento
                };

                // Chama o AppService para criar o modelo
                await modeloDocumentoAppService.CreateAsync(dto);

                return new JsonResult(new { success = true, message = "Modelo de documento criado com sucesso!" });
            }
            catch (UserFriendlyException ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = "Erro interno do servidor. Tente novamente." });
            }
        }

        private object GetModelStateErrors()
        {
            return ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );
        }
    }
}