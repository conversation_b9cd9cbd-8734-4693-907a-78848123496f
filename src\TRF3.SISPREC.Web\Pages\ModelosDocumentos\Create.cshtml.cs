using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using TRF3.SISPREC.Setores;
using TRF3.SISPREC.Setores.Dtos;
using TRF3.SISPREC.Web.Pages.ModelosDocumentos.ViewModels;

namespace TRF3.SISPREC.Web.Pages.ModelosDocumentos
{
    public class CreateModel(ISetorAppService setorService) : SISPRECPageModel
    {
        [BindProperty]
        public CreateModeloDocumentoViewModel ViewModel { get; set; } = new();

        public virtual async Task OnGetAsync()
        {
            var setores = await setorService.GetListAsync(new SetorGetListInput());

            ViewModel.SetorLookupList.AddRange(setores.Items.Select(x => new SelectListItem
            {
                Value = x.SetorId.ToString(),
                Text = x.Sigla
            }));
            ViewModel.SetorLookupList.AddFirst(new SelectListItem());
            await Task.CompletedTask;
        }
    }
}