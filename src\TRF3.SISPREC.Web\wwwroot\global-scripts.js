const formatarExibicaoDinheiro = (data) => data.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
const formatarExibicaoBool = (data) => data == null || data == undefined || data === "" ? "-" : data ? "Sim" : "Não";

$(document).ready(function () {
    $('abp-date-picker button[type="button"]').each(function () {
        if ($(this).closest('.input-group').find('.form-control-sm').length) {
            $(this).addClass('btn-sm');
        }
    });

    // Seleciona todos elementos data-datepicker dentro de abp-date-picker
    $(document).on('change', 'abp-date-picker [data-datepicker="true"]', function (e) {
        e.preventDefault();

        // Encontra o input hidden mais próximo dentro do mesmo abp-date-picker
        const hiddenInput = $(this).closest('abp-date-picker').find('input[type="hidden"]');

        if (hiddenInput.length) {
            // Dispara evento input no hidden
            hiddenInput.trigger('input');
        }
    });
});

// Ajusta as colunas da tabela ao redimensionar a janela para manter a responsividade
$(window).on('resize', function () {
    $('table.dataTable').DataTable().columns.adjust();
});
