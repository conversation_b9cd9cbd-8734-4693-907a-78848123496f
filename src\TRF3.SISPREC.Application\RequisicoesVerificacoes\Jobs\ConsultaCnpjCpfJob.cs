using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using TRF3.SISPREC.VerificacoesCnpjCpf;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class ConsultaCnpjCpfJob : AsyncBackgroundJob<ConsultaCnpjCpfArgs>, IConsultaCnpjCpfJob
{
    private readonly IVerificacaoCnpjCpfManager _controleCnpjCpfManager;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public ConsultaCnpjCpfJob(IVerificacaoCnpjCpfManager controleCnpjCpfManager, IUnitOfWorkManager unitOfWorkManager)
    {
        _controleCnpjCpfManager = controleCnpjCpfManager;
        _unitOfWorkManager = unitOfWorkManager;
    }

    public override async Task ExecuteAsync(ConsultaCnpjCpfArgs args)
    {
        try
        {
            if (args.RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar ConsultaCnpjCpfJob. RequisicaoVerificacaoId inválido: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
                return;
            }

            if (string.IsNullOrEmpty(args.NumeroProtocoloRequisicao))
            {
                Logger.LogError("Erro ao executar ConsultaCnpjCpfJob. NumeroProtocoloRequisicao inválido: NumeroProtocoloRequisicao: {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
                return;
            }

            using (var uow = _unitOfWorkManager.Begin(false, true))
            {
                await _controleCnpjCpfManager.ValidaRequisicaoControleCNPJeCPF(args.RequisicaoVerificacaoId, args.NumeroProtocoloRequisicao);
                await uow.CompleteAsync();
            }

        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar ConsultaCnpjCpfJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
            throw;
        }
    }
}

public class ConsultaCnpjCpfArgs : BaseBackgroundJobArgs
{
    public long RequisicaoVerificacaoId { get; set; }
    public string NumeroProtocoloRequisicao { get; set; }

    public override string JobGroupName => VerificacaoRequisicoesSettings.ConsultaCnpjCpfJobGroupName;

    public ConsultaCnpjCpfArgs(long requisicaoVerificacaoId, string numeroProtocoloRequisicao)
    {
        RequisicaoVerificacaoId = requisicaoVerificacaoId;
        NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
    }
}