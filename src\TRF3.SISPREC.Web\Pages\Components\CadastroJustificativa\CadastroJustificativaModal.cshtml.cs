using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicaoJustificativas.Dtos;
using TRF3.SISPREC.RequisicoesDocumentos;
using TRF3.SISPREC.RequisicoesDocumentos.Dtos;

namespace TRF3.SISPREC.Web.Pages.Components.CadastroJustificativas
{
    [ExcludeFromCodeCoverage]
    public class CadastroJustificativaModal : SISPRECPageModel
    {
        private readonly IRequisicaoJustificativaAppService _requisicaoJustificativaAppService;
        private readonly IRequisicaoDocumentoAppService _requisicaoDocumentoAppService;

        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public string? Id { get; set; }

        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public string? Procedimento { get; set; }

        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public EDescricaoAnaliseTela TipoAnalise { get; set; }

        [BindProperty]
        public CadastroJutificativaViewModel ViewModel { get; set; } = new();

        public CadastroJustificativaModal(IRequisicaoJustificativaAppService requisicaoJustificativaAppService,
                                          IRequisicaoDocumentoAppService requisicaoDocumentoAppService)
        {
            _requisicaoJustificativaAppService = requisicaoJustificativaAppService;
            _requisicaoDocumentoAppService = requisicaoDocumentoAppService;
        }


        public virtual async Task OnGetAsync()
        {
            ViewModel.RequisicaoList = Id.Split(',').ToList();
            ViewModel.Procedimento = Procedimento;
            ViewModel.TipoAnalise = TipoAnalise.GetEnumDescription();
            ViewModel.MotivoList =
            [
                new(string.Empty, null)
            ];
        }

        public virtual async Task<IActionResult> OnPostAsync()
        {
            ValidateModel();
            var input = new CreateUpdateJustificativaComplementoDto
            {
                Motivo = ViewModel.Motivo,
                ComplementoMotivo = ViewModel.ComplementoMotivo,
                Observacoes = ViewModel.Observacoes,
                RequisicaoList = ViewModel.RequisicaoList,
                RequisicoesComparadas = ViewModel.RequisicoesComparadas,
                TipoAnalise = TipoAnalise
            };

            await _requisicaoJustificativaAppService.CreateJustificativaCompletoAsync(input);

            if (int.Parse(ViewModel.Decisao!) == (int)EDescricaoAcaoTipo.CANCELAMENTO)
            {
                await _requisicaoDocumentoAppService.GerarEspelhoRequisicaoAsync(new CreateEspelhoRequisicaoDto()
                {
                    NumeroRequisicaoPrincipal = ViewModel.RequisicaoList,
                    Observacao = ViewModel.Observacoes,
                    Procedimento = ViewModel.Procedimento,
                    RequisicoesComparada = ViewModel.RequisicoesComparadas
                });
            }

            return NoContent();
        }
    }
}
