using TRF3.SISPREC.ViewProcessos.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.ViewProcessos;

public interface IViewProcessoAppService : IReadOnlyAppService<ViewProcessoDto, int, ViewProcessoGetListInput>

{
    Task<ViewProcessosGrupoStatusListDto> ListarStatus(ViewProcessoGetListInput input);
    Task<ViewProcessoFileDto> <PERSON><PERSON><PERSON><PERSON><PERSON>(int controleId, string numeroProcesso);
}