// Função auxiliar para verificar requisição
function verificarExistenciaRequisicao(newItemText, service) {
    abp.ui.block({ elm: '.modal-content', busy: true });

    service.existeRequisicao(newItemText)
        .then(response => {
            if (!response) {
                abp.message.info('Número de requisição não existe no SISPREC');
            }
        })
        .catch(() => {
            abp.message.error('Ocorreu um erro ao verificar a requisição');
        })
        .always(() => {
            abp.ui.unblock({ elm: '.modal-content' });
        });
}

// Função auxiliar para carregar motivos
function carregarMotivos(decisaoValue, service) {
    abp.ui.block({ elm: '.modal-content', busy: true });

    service.listarAcaoJustificativa(decisaoValue)
        .then(response => {
            const $select = $('#ViewModel_Motivo').empty();
            response.forEach(item => {
                $select.append(new Option(item.descricao, item.id));
            });
        })
        .catch(() => {
            abp.message.error('Ocorreu um erro ao buscar motivos');
        })
        .always(() => {
            abp.ui.unblock({ elm: '.modal-content' });
        });
}

// Função auxiliar para validar inclusão de requisição
function validarInclusaoRequisicao(newItemText) {
    const itemRequisicao = $('#ViewModel_RequisicaoList_0_').val()?.trim() || '';

    return validarRequisicaoInformada(newItemText) &&
        validarRequisicaoDiferente(newItemText, itemRequisicao) &&
        validarRequisicaoNaoDuplicada(newItemText);
}

// Funções auxiliares de validação
function validarRequisicaoInformada(newItemText) {
    if (!newItemText) {
        abp.message.info('Nenhuma requisição foi informada.');
        return false;
    }
    return true;
}

function validarRequisicaoDiferente(newItemText, itemRequisicao) {
    if (itemRequisicao.toLowerCase() === newItemText.toLowerCase()) {
        abp.message.info('A requisição comparada não pode ser a mesma para a qual está sendo cadastrada a justificativa.');
        return false;
    }
    return true;
}

function validarRequisicaoNaoDuplicada(newItemText) {
    const listItensComparada = $('#requisicoes-comparadas-table tr.item td.numero-requisicao')
        .map((_, el) => $(el).text().trim().toLowerCase()).get();

    if (listItensComparada.includes(newItemText.toLowerCase())) {
        abp.message.info('Requisição já incluída. Para editar as observações, remova a requisição e inclua novamente.');
        return false;
    }
    return true;
}

function verificaConfiguracaoDefault() {
    $(document).trigger('cadastroJustificativa:opened');
    if (typeof valueDecisao !== 'undefined' && typeof valueMotivo !== 'undefined') {
        carregaMotivosDefault(valueDecisao, valueMotivo);
    }
}

function carregaMotivosDefault(valueDecisao, valueMotivo) {
    $('#ViewModel_Decisao').val(valueDecisao).change();
    setTimeout(function () {
        $('#ViewModel_Motivo').val(valueMotivo);
    }, 500);
}