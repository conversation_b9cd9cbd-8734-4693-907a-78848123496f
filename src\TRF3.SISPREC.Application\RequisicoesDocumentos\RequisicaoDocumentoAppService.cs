using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.RequisicoesDocumentos.Dtos;
using TRF3.SISPREC.RequisicoesPropostas;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.RequisicoesDocumentos;
public class RequisicaoDocumentoAppService : BaseCrudAppService<RequisicaoDocumento, RequisicaoDocumentoDto, long, RequisicaoDocumentoGetListInput, CreateUpdateRequisicaoDocumentoDto, CreateUpdateRequisicaoDocumentoDto>,
    IRequisicaoDocumentoAppService
{
    private readonly IRequisicaoPropostaRepository _requisicaoPropostaRepository;
    private readonly IRequisicaoDocumentoManager _manager;
    private readonly IRequisicaoDocumentoRepository _repository;
    private readonly IRequisicaoDocumentoService _service;

    public RequisicaoDocumentoAppService(
        IRequisicaoPropostaRepository requisicaoPropostaRepository,
        IRequisicaoDocumentoRepository repository,
        IRequisicaoDocumentoManager manager,
        IRequisicaoDocumentoService service
        ) : base(repository, manager)
    {
        _requisicaoPropostaRepository = requisicaoPropostaRepository ?? throw new ArgumentNullException(nameof(requisicaoPropostaRepository));
        _service = service ?? throw new ArgumentNullException(nameof(service));
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _manager = manager ?? throw new ArgumentNullException(nameof(manager));
    }

    protected override Task DeleteByIdAsync(long id)
    {
        return _manager.ExcluirAsync(e =>
            e.RequisicaoDocumentoId == id
        );
    }


    public async Task GerarEspelhoRequisicaoAsync(CreateEspelhoRequisicaoDto input)
    {
        try
        {
            foreach (string requisicaoPrincipal in input.NumeroRequisicaoPrincipal)
            {
                var proposta = await (await _requisicaoPropostaRepository.GetQueryableAsync())
                    .Include(x => x.Proposta)
                    .Where(x => x.NumeroProtocoloRequisicao == requisicaoPrincipal)
                    .Select(x => new { x.Proposta.AnoProposta, x.Proposta.MesProposta })
                    .FirstOrDefaultAsync();

                if (proposta is null)
                    throw new ArgumentException($"Não foi possivel localizar a proposta para o numero da requisicao {input.NumeroRequisicaoPrincipal}");

                await _service.GerarEspelhoRequisicaoAsync(requisicaoPrincipal, input.Procedimento, proposta.AnoProposta, proposta.MesProposta, input.Observacao);

                //Gerar requisições comparadas
                if (input.RequisicoesComparada is not null)
                {
                    foreach (var requisicaoComparada in input.RequisicoesComparada)
                    {
                        //ignora caso a requisição não exista no sisprec.
                        var existeRequisicao = await _requisicaoPropostaRepository.AnyAsync(x => x.NumeroProtocoloRequisicao == requisicaoComparada.NumeroRequisicao);
                        if (!existeRequisicao)
                            continue;

                        await _service.GerarEspelhoRequisicaoAsync(requisicaoComparada.NumeroRequisicao, input.Procedimento, proposta.AnoProposta, proposta.MesProposta, requisicaoComparada.Observacoes, requisicaoPrincipal);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException(ex.Message, innerException: ex);
        }
    }

    public async Task ExcluirEspelhoRequisicaoAsync(string numeroRequisicao)
    {
        await _service.ExcluirEspelhoRequisicaoAsync(numeroRequisicao);
    }

    protected override async Task<RequisicaoDocumento> GetEntityByIdAsync(long id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.RequisicaoDocumentoId == id
            ));
    }

    protected override IQueryable<RequisicaoDocumento> ApplyDefaultSorting(IQueryable<RequisicaoDocumento> query)
    {
        return query.OrderBy(e => e.RequisicaoDocumentoId);
    }

    protected override async Task<IQueryable<RequisicaoDocumento>> CreateFilteredQueryAsync(RequisicaoDocumentoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.RequisicaoDocumentoId != null, x => x.RequisicaoDocumentoId == input.RequisicaoDocumentoId)
            .WhereIf(!input.NomeDocumento.IsNullOrWhiteSpace(), x => x.NomeDocumento.Contains(input.NomeDocumento))
            .WhereIf(!input.Path.IsNullOrWhiteSpace(), x => x.Path.Contains(input.Path))
            .WhereIf(input.DataCriacao != null, x => x.DataCriacao == input.DataCriacao)
            .WhereIf(!input.NumeroProtocoloRequisicao.IsNullOrWhiteSpace(), x => x.NumeroProtocoloRequisicao.Contains(input.NumeroProtocoloRequisicao))
            .WhereIf(input.IsDeleted != null, x => x.IsDeleted == input.IsDeleted)
            ;
    }
}
