using TRF3.SISPREC.SincronizacaoLegado.Models.ReqPag;
using Volo.Abp.DependencyInjection;

namespace TRF3.SISPREC.SincronizacaoLegado.Repositories.ReqPag
{
    public interface IPropostaReqPagRepository : ITransientDependency
    {
        Task<IEnumerable<Proposta>> BuscaIndiceEntidade(Proposta propostaDTO);

        Task<IEnumerable<Proposta>> BuscaIndice(Proposta propostaDTO);

        Task<IEnumerable<Proposta>> BuscaPropostasRPVDoisUltimosMesesEProximo();
        Task<IEnumerable<Proposta>> BuscaPropostasPCTAnoMaiorOuIgualAoAtual();

        Task UpdateFechaProposta(IList<Proposta> propostaDTO);

        Task<IEnumerable<Proposta>> BuscaEntidadeNaoFechada(Proposta propostaDTO);

        Task<IEnumerable<Proposta>> BuscaEntidade(Proposta propostaDTO, string Entidade);
    }
}
