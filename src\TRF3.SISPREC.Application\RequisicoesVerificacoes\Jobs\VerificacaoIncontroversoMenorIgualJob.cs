using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using TRF3.SISPREC.VerificacaoIncontroversoMenorIgual;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoIncontroversoMenorIgualJob : AsyncBackgroundJob<VerificacaoIncontroversoMenorIgualJobArgs>, IVerificacaoIncontroversoMenorIgualJob
{
    private readonly IVerificacaoIncontroversoMenorIgualService _verificacaoIncontroversoMenorIgualService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public VerificacaoIncontroversoMenorIgualJob(IVerificacaoIncontroversoMenorIgualService verificacaoIncontroversoMenorIgualService, IUnitOfWorkManager unitOfWorkManager)
    {
        _verificacaoIncontroversoMenorIgualService = verificacaoIncontroversoMenorIgualService;
        _unitOfWorkManager = unitOfWorkManager;
    }

    [ExcludeFromCodeCoverage]
    public override async Task ExecuteAsync(VerificacaoIncontroversoMenorIgualJobArgs args)
    {
        try
        {
            if (args.RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar {Job}. RequisicaoVerificacaoId inválido: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", nameof(VerificacaoIncontroversoMenorIgualJob), args.RequisicaoVerificacaoId);
                return;
            }

            if (string.IsNullOrEmpty(args.NumeroProtocoloRequisicao))
            {
                Logger.LogError("Erro ao executar {Job}. NumeroProtocoloRequisicao inválido: NumeroProtocoloRequisicao: {NumeroProtocoloRequisicao}.", nameof(VerificacaoIncontroversoMenorIgualJob), args.NumeroProtocoloRequisicao);
                return;
            }

            using IUnitOfWork uow = _unitOfWorkManager.Begin(false, true);
            await _verificacaoIncontroversoMenorIgualService.VerificarIncontroversoAsync(args.RequisicaoVerificacaoId, args.NumeroProtocoloRequisicao);
            await uow.CompleteAsync();

        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar {Job}: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", nameof(VerificacaoIncontroversoMenorIgualJob), args.RequisicaoVerificacaoId);
            throw;
        }
    }
}

[ExcludeFromCodeCoverage]
public class VerificacaoIncontroversoMenorIgualJobArgs : BaseBackgroundJobArgs
{
    public long RequisicaoVerificacaoId { get; set; }
    public string NumeroProtocoloRequisicao { get; set; }

    public override string JobGroupName => VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualJobGroupName;

    public VerificacaoIncontroversoMenorIgualJobArgs(long requisicaoVerificacaoId, string numeroProtocoloRequisicao)
    {
        RequisicaoVerificacaoId = requisicaoVerificacaoId;
        NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
    }
}