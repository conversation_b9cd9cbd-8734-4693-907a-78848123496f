using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using TRF3.SISPREC.VerificacaoPrevencoes;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs
{
    [ExcludeFromCodeCoverage]
    public class VerificacaoPrevencaoTipo32Job : AsyncBackgroundJob<VerificacaoPrevencaoTipo32Args>, IVerificacaoPrevencaoTipo32Job
    {
        #region Read-Only Fields

        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly IVerificacaoPrevencaoManager _verificacaoPrevencaoManager;

        #endregion

        #region Constructors

        public VerificacaoPrevencaoTipo32Job(IUnitOfWorkManager unitOfWorkManager,
                                             IVerificacaoPrevencaoManager verificacaoPrevencaoManager)
        {
            _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
            _verificacaoPrevencaoManager = verificacaoPrevencaoManager ?? throw new ArgumentNullException(nameof(verificacaoPrevencaoManager));
        }

        #endregion

        #region ExecuteAsync

        public override async Task ExecuteAsync(VerificacaoPrevencaoTipo32Args args)
        {
            try
            {
                using (var uow = _unitOfWorkManager.Begin(false, true))
                {
                    await _verificacaoPrevencaoManager.ProcessarVerificacaoAsync(
                        args.NumeroProtocoloRequisicao, Enums.EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_32);
                    await uow.CompleteAsync();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Erro ao executar VerificacaoPrevencaoTipo32Job para requisição nº {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
                throw;
            }
        }

        #endregion
    }

    [ExcludeFromCodeCoverage]
    public class VerificacaoPrevencaoTipo32Args : BaseBackgroundJobArgs
    {
        public string NumeroProtocoloRequisicao { get; set; }

        public override string JobGroupName => VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo32JobGroupName;

        public VerificacaoPrevencaoTipo32Args(string numeroProtocoloRequisicao)
        {
            if (string.IsNullOrEmpty(numeroProtocoloRequisicao) || string.IsNullOrWhiteSpace(numeroProtocoloRequisicao))
                throw new ArgumentException($"Erro ao executar verificação de prevenção tipo 32. Nº requisição inválido: {numeroProtocoloRequisicao}.");

            NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
        }
    }
}
