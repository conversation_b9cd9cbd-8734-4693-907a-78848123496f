using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.RequisicaoJustificativas.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.RequisicoesDocumentos.Dtos
{
    [ExcludeFromCodeCoverage]
    public class CreateEspelhoRequisicaoDto : EntityDto, IValidatableObject
    {
        public List<string> NumeroRequisicaoPrincipal { get; set; } = new List<string>();
        public string Observacao { get; set; }
        public string Procedimento { get; set; }
        public List<RequisicaoComparadaDto> RequisicoesComparada { get; set; } = new List<RequisicaoComparadaDto>();


        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (RequisicoesComparada != null && RequisicoesComparada.Any())
            {
                if (RequisicoesComparada.Any(x => NumeroRequisicaoPrincipal.Contains(x.NumeroRequisicao)))
                    yield return new ValidationResult("A requisição comparada não pode ser a mesma para a qual está sendo cadastrada a justificativa.");

                bool existeDuplicada = RequisicoesComparada
                    .GroupBy(x => x.NumeroRequisicao)
                    .Any(group => group.Count() > 1);

                if (existeDuplicada)
                    yield return new ValidationResult("Requisição já incluída. Para editar as observações, remova a requisição e inclua novamente.");
            }
        }
    }
}
