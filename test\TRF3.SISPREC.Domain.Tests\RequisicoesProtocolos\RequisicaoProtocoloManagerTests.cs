using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using MockQueryable.NSubstitute;
using NSubstitute;
using System.Linq.Expressions;
using TRF3.SISPREC.Assuntos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.IndicadorEconomicoTipos;
using TRF3.SISPREC.ProcessoOrigens;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicaoEstornos;
using TRF3.SISPREC.RequisicaoObservacoes;
using TRF3.SISPREC.REquisicoesExpedientesAdministrativos;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesPartes;
using TRF3.SISPREC.RequisicoesPlanosOrcamentos;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos;
using TRF3.SISPREC.RequisicoesProtocolos.AssuntosExecucoes;
using TRF3.SISPREC.RequisicoesVerificacoes;
using TRF3.SISPREC.SincronizacaoLegado;
using TRF3.SISPREC.SituacoesRequisicoesProtocolos;
using TRF3.SISPREC.UnidadesJudiciais;
using TRF3.SISPREC.VerificacaoTipos;
using Volo.Abp;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.Tests.RequisicoesProtocolos;

public class RequisicaoProtocoloManagerTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
{
    #region Read-Only Fields
    private readonly IRequisicaoProtocoloRepository _repository;
    private RequisicaoProtocoloManager _manager;

    private readonly IReqPagUnitOfWork _reqPagUnitOfWork;


    private IRequisicaoPropostaRepository _requisicaoPropostaRepository;
    private IPropostaRepository _propostaRepository;
    private ISituacaoRequisicaoProtocoloRepository _situacaoRequisicaoProtocoloRepository;
    private IAssuntoRepository _assuntoRepository;
    private IUnidadeJudicialRepository _unidadeJudicialRepository;
    private IVerificacaoTipoRepository _verificacaoTipoRepository;
    private IIndicadorEconomicoTipoRepository _tipoIndicadorEconomicoRepository;
    private IRequisicaoVerificacaoRepository _requisicaoVerificacaoRepository;
    private IRequisicaoEstornoRepository _requisicaEstornoRepository;
    private IAssuntoExecucaoManager _assuntoExecucaoManager;
    private IRequisicaoParteManager _requisicaoParteManager;
    private IProcessoOrigemManager _processoOrigemManager;
    private IRequisicaoObservacaoManager _requisicaoObservacaoManager;
    private IRequisicaoExpedienteAdministrativoManager _requisicaoExpedienteAdministrativoManager;

    private IImportarRequisicoesOcorrenciasService _importarRequisicoesOcorrenciasService;
    private IRequisicaoVerificacaoManager _requisicaoVerificacaoManager;
    private IImportarRequisicoesPlanosOrcamentosService _importarRequisicoesPlanosOrcamentosService;

    #endregion
    #region Construtor
    public RequisicaoProtocoloManagerTests()
    {
        _repository = Substitute.For<IRequisicaoProtocoloRepository>();
        _reqPagUnitOfWork = Substitute.For<IReqPagUnitOfWork>();

        _manager = new RequisicaoProtocoloManager(_repository, _reqPagUnitOfWork);

        _manager.LazyServiceProvider ??= new AbpLazyServiceProvider(ServiceProvider);

    }


    protected override void AfterAddApplication(IServiceCollection services)
    {
        _tipoIndicadorEconomicoRepository = Substitute.For<IIndicadorEconomicoTipoRepository>();
        _situacaoRequisicaoProtocoloRepository = Substitute.For<ISituacaoRequisicaoProtocoloRepository>();
        _requisicaoPropostaRepository = Substitute.For<IRequisicaoPropostaRepository>();
        _propostaRepository = Substitute.For<IPropostaRepository>();
        _assuntoRepository = Substitute.For<IAssuntoRepository>();
        _unidadeJudicialRepository = Substitute.For<IUnidadeJudicialRepository>();
        _verificacaoTipoRepository = Substitute.For<IVerificacaoTipoRepository>();
        _requisicaoVerificacaoRepository = Substitute.For<IRequisicaoVerificacaoRepository>();
        _assuntoExecucaoManager = Substitute.For<IAssuntoExecucaoManager>();
        _requisicaoParteManager = Substitute.For<IRequisicaoParteManager>();
        _processoOrigemManager = Substitute.For<IProcessoOrigemManager>();
        _requisicaoObservacaoManager = Substitute.For<IRequisicaoObservacaoManager>();
        _requisicaoExpedienteAdministrativoManager = Substitute.For<IRequisicaoExpedienteAdministrativoManager>();
        _importarRequisicoesPlanosOrcamentosService = Substitute.For<IImportarRequisicoesPlanosOrcamentosService>();

        _importarRequisicoesOcorrenciasService = Substitute.For<IImportarRequisicoesOcorrenciasService>();
        _requisicaoVerificacaoManager = Substitute.For<IRequisicaoVerificacaoManager>();
        _requisicaEstornoRepository = Substitute.For<IRequisicaoEstornoRepository>();
        //services.RemoveAll(typeof(IUnitOfWorkManager));
        //services.AddTransient(typeof(IUnitOfWorkManager), _ => _unitOfWorkManager);
        services.RemoveAll(typeof(IAssuntoExecucaoManager));
        services.AddTransient(typeof(IAssuntoExecucaoManager), _ => _assuntoExecucaoManager);
        services.RemoveAll(typeof(IRequisicaoParteManager));
        services.AddTransient(typeof(IRequisicaoParteManager), _ => _requisicaoParteManager);
        services.RemoveAll(typeof(IProcessoOrigemManager));
        services.AddTransient(typeof(IProcessoOrigemManager), _ => _processoOrigemManager);
        services.RemoveAll(typeof(IRequisicaoObservacaoManager));
        services.AddTransient(typeof(IRequisicaoObservacaoManager), _ => _requisicaoObservacaoManager);
        services.RemoveAll(typeof(IRequisicaoExpedienteAdministrativoManager));
        services.AddTransient(typeof(IRequisicaoExpedienteAdministrativoManager), _ => _requisicaoExpedienteAdministrativoManager);
        services.RemoveAll(typeof(IRequisicaoVerificacaoManager));
        services.AddTransient(typeof(IRequisicaoVerificacaoManager), _ => _requisicaoVerificacaoManager);

        services.RemoveAll(typeof(ISituacaoRequisicaoProtocoloRepository));
        services.AddTransient(typeof(ISituacaoRequisicaoProtocoloRepository), _ => _situacaoRequisicaoProtocoloRepository);
        services.RemoveAll(typeof(IRequisicaoPropostaRepository));
        services.AddTransient(typeof(IRequisicaoPropostaRepository), _ => _requisicaoPropostaRepository);
        services.RemoveAll(typeof(IPropostaRepository));
        services.AddTransient(typeof(IPropostaRepository), _ => _propostaRepository);
        services.RemoveAll(typeof(IAssuntoRepository));
        services.AddTransient(typeof(IAssuntoRepository), _ => _assuntoRepository);
        services.RemoveAll(typeof(IUnidadeJudicialRepository));
        services.AddTransient(typeof(IUnidadeJudicialRepository), _ => _unidadeJudicialRepository);
        services.RemoveAll(typeof(IVerificacaoTipoRepository));
        services.AddTransient(typeof(IVerificacaoTipoRepository), _ => _verificacaoTipoRepository);
        services.RemoveAll(typeof(IIndicadorEconomicoTipoRepository));
        services.AddTransient(typeof(IIndicadorEconomicoTipoRepository), _ => _tipoIndicadorEconomicoRepository);
        services.RemoveAll(typeof(IRequisicaoVerificacaoRepository));
        services.AddTransient(typeof(IRequisicaoVerificacaoRepository), _ => _requisicaoVerificacaoRepository);
        services.RemoveAll(typeof(IRequisicaoEstornoRepository));
        services.AddTransient(typeof(IRequisicaoEstornoRepository), _ => _requisicaEstornoRepository);
        services.RemoveAll(typeof(IImportarRequisicoesOcorrenciasService));
        services.AddTransient(typeof(IImportarRequisicoesOcorrenciasService), _ => _importarRequisicoesOcorrenciasService);
        services.RemoveAll(typeof(IImportarRequisicoesPlanosOrcamentosService));
        services.AddTransient(typeof(IImportarRequisicoesPlanosOrcamentosService), _ => _importarRequisicoesPlanosOrcamentosService);

        base.AfterAddApplication(services);
    }
    #endregion
    #region Menbers
    private RequisicaoProtocolo InicializarRequisicaoProtocoloComValoresValidos()
    {
        return new Bogus.Faker<RequisicaoProtocolo>("pt_BR")
        .RuleFor(p => p.NumeroProtocoloRequisicao, p => p.Random.Hash(11)) // CHAR(11)
        .RuleFor(p => p.AssuntoId, p => p.Random.Int())
        .RuleFor(p => p.TipoProcedimentoId, p => p.Random.Hash(3)) // CHAR(3)
        .RuleFor(p => p.TipoHonorario, p => p.PickRandom<ETipoHonorario>())
        .RuleFor(p => p.RenunciaValorLimite, p => p.Random.Bool())
        .RuleFor(p => p.ValorRequisicao, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(18,4)
        .RuleFor(p => p.DataContaLiquidacao, p => p.Date.Past()) // DATE
        .RuleFor(p => p.TipoRequisicao, p => p.PickRandom<ETipoRequisicao>())
        .RuleFor(p => p.ValorConta, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(18,4)
        .RuleFor(p => p.DataConta, p => p.Date.Past()) // DATE
        .RuleFor(p => p.DataTransitoJulgadoFase, p => p.Date.Past()) // DATE
        .RuleFor(p => p.DataTransitoJulgadoEmbargos, p => p.Date.Past()) // DATE
        .RuleFor(p => p.NomeMagistrado, p => p.Random.Hash(20)) // VARCHAR(60)
        .RuleFor(p => p.DesignacaoMagistrado, p => p.PickRandom<EDesignacaoMagistrado>())
        .RuleFor(p => p.NaturezaCredito, p => p.PickRandom<ENaturezaCredito>())
        .RuleFor(p => p.DesapropriacaoUnicoImovel, p => p.Random.Bool())
        .RuleFor(p => p.ValorAtualizadoRequisicao, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(18,4)
        .RuleFor(p => p.SituacaoRequisicaoId, p => p.Random.Int())
        .RuleFor(p => p.ExecucaoFiscal, p => p.Random.Bool())
        .RuleFor(p => p.ValorCompensacao, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(18,4)
        .RuleFor(p => p.BloqueioDepositoJudicial, p => p.Random.Bool())
        .RuleFor(p => p.LevantamentoOrdemJuizo, p => p.Random.Bool())
        .RuleFor(p => p.DataTransitoDeferimentoCompensacao, p => p.Date.Past()) // DATE
        .RuleFor(p => p.ValorCompensacaoAtualizado, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(18,4)
        .RuleFor(p => p.ValorRequisicaoPrincipal, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.ValorRequisicaoJuros, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.ValorContaPrincipal, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.ValorContaJuros, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.ValorAtualizadoRequisicaoPrincipal, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.ValorAtualizadoRequisicaoJuros, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.Selic, p => p.Random.Bool())
        .RuleFor(p => p.ValorTotalReferencia, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.IndicadorJurosMora, p => p.PickRandom<EIndicadorJurosMora>())
        .RuleFor(p => p.ValorAliquotaJurosMora, p => p.Random.Decimal(0, 999999.9999m)) // DECIMAL(6,4)
        .RuleFor(p => p.ValorJurosMora, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
            .Generate();
    }


    [Fact]
    public async Task Inserir_Muitos_Assincronamente()
    {
        var objeto = InicializarRequisicaoProtocoloComValoresValidos();
        var objetos = new List<RequisicaoProtocolo> { objeto };
        await _manager.InserirMuitosAsync(objetos);
        await _repository.Received(1).InsertManyAsync(objetos, Arg.Any<bool>(), Arg.Any<CancellationToken>());
    }


    [Fact]
    public async Task ImportarParaControle_Deve_Importar_Controle_Corretamente()
    {
        // Arrange
        var proposta = CreateRandomProposta();
        var requisicoes = InicializarReqProcotoloNaoImportadas();
        var situacaoProtocolo = InicializaSituacaoProtocolo();
        var assunto = InicializaAssunto();
        var unidadeJudicial = InicializarUnidadeJudicial();
        var verificacaoTipo = InicializarRequisicaoVerificacao();
        var parcelas = InicializaRequisicaoParcelaProtocoloReqPag();
        var estornos = InicializaEstorno();

        var requisicaoEstorno = new RequisicaoEstorno
        {
            CodigoBeneficiario = estornos.cod_benefi,
            DataHoraProtocoloRequisicao = (DateTime)estornos.dat_hora_protoc_requis,
            DataRecolhimentoConta = (DateTime)estornos.dat_recolh_conta,
            NomeBeneficiario = estornos.nom_benefi,
            NumeroBanco = estornos.num_banco,
            NumeroContaCorrente = estornos.num_conta_corren,
            NumeroProtocoloRequisicaoId = "12345",
            NumeroRequisicaoOriginal = estornos.num_requis,
            ValorRecolhimentoConta = (double)estornos.val_recolh_conta
        };


        _situacaoRequisicaoProtocoloRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<SituacaoRequisicaoProtocolo, bool>>>()).Returns(situacaoProtocolo);
        _reqPagUnitOfWork.RequisicaoEstornoRepository.BuscaPorRequis(Arg.Any<string>()).Returns(estornos);

        _reqPagUnitOfWork.RequisicaoProtocoloRepository.BuscaPorPropostaNaoImportadasPorProtocolo(Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>()).Returns(requisicoes[0]);
        _reqPagUnitOfWork.RequisPropostaParcelaRepository.BuscaPorRequisicaoLista(Arg.Any<string>()).Returns(parcelas);
        _assuntoRepository.ObterAssuntoImportarReqPagAsync(Arg.Any<string>()).Returns(assunto);

        _unidadeJudicialRepository.GetAsync(Arg.Any<Expression<Func<UnidadeJudicial, bool>>>()).Returns(Task.FromResult(unidadeJudicial));

        _verificacaoTipoRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<VerificacaoTipo, bool>>>()).Returns(verificacaoTipo);

        _tipoIndicadorEconomicoRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<IndicadorEconomicoTipo, bool>>>()).Returns(new IndicadorEconomicoTipo(1, requisicoes[0].cod_tipo_indica_econom, string.Empty));

        _requisicaEstornoRepository.InsertAsync(Arg.Any<RequisicaoEstorno>()).ReturnsForAnyArgs(Task.FromResult(requisicaoEstorno));

        await _requisicaoVerificacaoManager.InserirPorRequisicao(Arg.Any<string>(), Arg.Any<List<EVerificacaoRequisicaoTipo>>());

        // Act  
        var queryable = new List<Proposta>()
            {
                proposta
            }.AsQueryable();

        var buildMock = queryable.BuildMockDbSet();
        _propostaRepository.GetQueryableAsync().Returns(buildMock);

        _propostaRepository.PropostaComDetalhes().Returns(queryable);

        await _manager.InserirImportacaoAsync(proposta.PropostaId, "12345");

        // Assert
        await _requisicaoPropostaRepository
        .Received(1)
        .InsertAsync(Arg.Is<RequisicaoProposta>(x => x.NumeroProtocoloRequisicao == "12345" && x.PropostaId == 1), true);
    }


    [Fact]
    public async Task InserirImportacaoAsync_PropostaInexistente_ReturnsNull()
    {
        // Arrange
        long propostaId = 1;
        string numProtocolo = "123456";

        _propostaRepository.PropostaComDetalhes().Returns(new List<Proposta>().AsQueryable());

        // Act && Assert
        await Assert.ThrowsAsync<UserFriendlyException>(async () => await _manager.InserirImportacaoAsync(propostaId, numProtocolo));
    }

    [Fact]
    public async Task InserirImportacaoAsync_ReturnsException_Assunto()
    {
        // Arrange
        var proposta = CreateRandomProposta();
        var requisicoes = InicializarReqProcotoloNaoImportadas();
        var situacaoProtocolo = InicializaSituacaoProtocolo();

        await WithUnitOfWorkAsync(async () =>
        {
            _reqPagUnitOfWork.RequisicaoProtocoloRepository.BuscaPorPropostaNaoImportadasPorProtocolo(Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>()).Returns(requisicoes[0]);

            _situacaoRequisicaoProtocoloRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<SituacaoRequisicaoProtocolo, bool>>>()).Returns(situacaoProtocolo);

            _assuntoRepository.ObterAssuntoImportarReqPagAsync(Arg.Any<string>()).Returns((Assunto?)null);

            // Act
            var queryable = new List<Proposta>()
            {
                proposta
            }.AsQueryable();

            _propostaRepository.PropostaComDetalhes().Returns(queryable);

            // Assert
            var exception = await Assert.ThrowsAsync<UserFriendlyException>(async () => await _manager.InserirImportacaoAsync(proposta.PropostaId, "12345"));
            Assert.Equal($"Assunto não encontrado! para Assunto de código: {requisicoes[0].cod_assunt}", exception.Message);
        });
    }

    [Fact]
    public async Task InserirImportacaoAsync_ReturnsException_SituacaoRequisicao()
    {
        // Arrange
        //var unitOfWork = Substitute.For<IUnitOfWork>();
        //_unitOfWorkManager.Begin(requiresNew: true, isTransactional: true).Returns(unitOfWork);
        var proposta = CreateRandomProposta();
        var requisicoes = InicializarReqProcotoloNaoImportadas();

        await WithUnitOfWorkAsync(async () =>
        {
            _situacaoRequisicaoProtocoloRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<SituacaoRequisicaoProtocolo, bool>>>()).Returns((SituacaoRequisicaoProtocolo?)null);

            _reqPagUnitOfWork.RequisicaoProtocoloRepository.BuscaPorPropostaNaoImportadasPorProtocolo(Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>()).Returns(requisicoes[0]);

            // Act
            var queryable = new List<Proposta>()
            {
                proposta
            }.AsQueryable();

            _propostaRepository.PropostaComDetalhes().Returns(queryable);

            // Assert
            var exception = await Assert.ThrowsAsync<UserFriendlyException>(async () => await _manager.InserirImportacaoAsync(proposta.PropostaId, "12345"));
            Assert.Equal($"Protocolo de Requisição de Situação não encontrada! para Código situação requisição: {requisicoes[0].cod_situac_requis}", exception.Message);

        });
    }


    [Fact]
    public async Task ObterRequisicaoProposta_Deve__ReturnsException_Indicador()
    {
        var proposta = CreateRandomProposta();
        var requisicoes = InicializarReqProcotoloNaoImportadas();
        var situacaoProtocolo = InicializaSituacaoProtocolo();
        var assunto = InicializaAssunto();
        var unidadeJudicial = InicializarUnidadeJudicial();
        var verificacaoTipo = InicializarRequisicaoVerificacao();
        var parcelas = InicializaRequisicaoParcelaProtocoloReqPag();
        var estornos = InicializaEstorno();

        var requisicaoEstorno = new RequisicaoEstorno
        {
            CodigoBeneficiario = estornos.cod_benefi,
            DataHoraProtocoloRequisicao = (DateTime)estornos.dat_hora_protoc_requis,
            DataRecolhimentoConta = (DateTime)estornos.dat_recolh_conta,
            NomeBeneficiario = estornos.nom_benefi,
            NumeroBanco = estornos.num_banco,
            NumeroContaCorrente = estornos.num_conta_corren,
            NumeroProtocoloRequisicaoId = "12345",
            NumeroRequisicaoOriginal = estornos.num_requis,
            ValorRecolhimentoConta = (double)estornos.val_recolh_conta
        };

        await WithUnitOfWorkAsync(async () =>
        {



            _situacaoRequisicaoProtocoloRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<SituacaoRequisicaoProtocolo, bool>>>()).Returns(situacaoProtocolo);
            _reqPagUnitOfWork.RequisicaoEstornoRepository.BuscaPorRequis(Arg.Any<string>()).Returns(estornos);

            _reqPagUnitOfWork.RequisicaoProtocoloRepository.BuscaPorPropostaNaoImportadasPorProtocolo(Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>()).Returns(requisicoes[0]);
            _reqPagUnitOfWork.RequisPropostaParcelaRepository.BuscaPorRequisicaoLista(Arg.Any<string>()).Returns(parcelas);
            _assuntoRepository.ObterAssuntoImportarReqPagAsync(Arg.Any<string>()).Returns(assunto);

            _unidadeJudicialRepository.GetAsync(Arg.Any<Expression<Func<UnidadeJudicial, bool>>>()).Returns(Task.FromResult(unidadeJudicial));

            _verificacaoTipoRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<VerificacaoTipo, bool>>>()).Returns(verificacaoTipo);

            _tipoIndicadorEconomicoRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<IndicadorEconomicoTipo, bool>>>()).Returns((IndicadorEconomicoTipo)null);

            _requisicaEstornoRepository.InsertAsync(Arg.Any<RequisicaoEstorno>()).ReturnsForAnyArgs(Task.FromResult(requisicaoEstorno));

            await _requisicaoVerificacaoManager.InserirPorRequisicao(Arg.Any<string>(), Arg.Any<List<EVerificacaoRequisicaoTipo>>());

            // Act  
            var queryable = new List<Proposta>()
            {
                proposta
            }.AsQueryable();

            var buildMock = queryable.BuildMockDbSet();
            _propostaRepository.GetQueryableAsync().Returns(buildMock);

            _propostaRepository.PropostaComDetalhes().Returns(queryable);

            // Assert
            var exception = await Assert.ThrowsAsync<UserFriendlyException>(async () => await _manager.InserirImportacaoAsync(proposta.PropostaId, "12345"));
            Assert.Equal($"Não foi possível encontrar um tipo de indicador econômico com o código {requisicoes[0].cod_tipo_indica_econom}", exception.Message);

        });
    }


    [Fact]
    public async Task InserirImportacaoAsync_Deve__ReturnsException_requisicoesReqPag()
    {
        var proposta = CreateRandomProposta();

        await WithUnitOfWorkAsync(async () =>
        {

            _reqPagUnitOfWork.RequisicaoProtocoloRepository.BuscaPorPropostaNaoImportadasPorProtocolo(Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>()).Returns((SincronizacaoLegado.Models.ReqPag.RequisicaoProtocoloImportacao)null);

            await _requisicaoVerificacaoManager.InserirPorRequisicao(Arg.Any<string>(), Arg.Any<List<EVerificacaoRequisicaoTipo>>());

            // Act  
            var queryable = new List<Proposta>()
            {
                proposta
            }.AsQueryable();

            var buildMock = queryable.BuildMockDbSet();
            _propostaRepository.GetQueryableAsync().Returns(buildMock);

            _propostaRepository.PropostaComDetalhes().Returns(queryable);

            // Assert
            var exception = await Assert.ThrowsAsync<UserFriendlyException>(async () => await _manager.InserirImportacaoAsync(proposta.PropostaId, "12345"));
            Assert.Equal($"Requisição não encontrada! para o Tipo procedimento id: {proposta.TipoProcedimentoId} e Ano proposta: {proposta.AnoProposta} e Mes proposta: {proposta.MesProposta} e Numero protocolo: 12345", exception.Message);

        });
    }

    [Fact]
    public async Task InserirImportacaoAsync_ReturnsException_UnidadeJudicial()
    {
        // Arrange
        //var unitOfWork = Substitute.For<IUnitOfWork>();
        //_unitOfWorkManager.Begin().Returns(unitOfWork);
        var proposta = CreateRandomProposta();
        var requisicoes = InicializarReqProcotoloNaoImportadas();
        var situacaoProtocolo = InicializaSituacaoProtocolo();
        var assunto = InicializaAssunto();

        await WithUnitOfWorkAsync(async () =>
        {
            _situacaoRequisicaoProtocoloRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<SituacaoRequisicaoProtocolo, bool>>>()).Returns(situacaoProtocolo);

            _assuntoRepository.ObterAssuntoImportarReqPagAsync(Arg.Any<string>()).Returns(assunto);

            _reqPagUnitOfWork.RequisicaoProtocoloRepository.BuscaPorPropostaNaoImportadasPorProtocolo(Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>()).Returns(requisicoes[0]);

            // Act  
            var queryable = new List<Proposta>()
            {
                proposta
            }.AsQueryable();

            _propostaRepository.PropostaComDetalhes().Returns(queryable);

            // Assert
            var exception = await Assert.ThrowsAsync<UserFriendlyException>(async () => await _manager.InserirImportacaoAsync(proposta.PropostaId, "12345"));
            Assert.Equal($"Unidade Judicial não encontrado! para o Codigo siafi juizo origem: {requisicoes[0].cod_siafi_juizo_origem}", exception.Message);

        });
    }


    [Fact]
    public async Task InserirImportacaoAsync_ReturnsException_TipoEconomicoParcela()
    {
        // Arrange
        //var unitOfWork = Substitute.For<IUnitOfWork>();
        //_unitOfWorkManager.Begin().Returns(unitOfWork);
        var proposta = CreateRandomProposta();
        var requisicoes = InicializarReqProcotoloNaoImportadas();
        var situacaoProtocolo = InicializaSituacaoProtocolo();
        var assunto = InicializaAssunto();
        var unidadeJudicial = InicializarUnidadeJudicial();
        var verificacaoTipo = InicializarRequisicaoVerificacao();
        var parcelas = InicializaRequisicaoParcelaProtocoloReqPag();
        var estornos = InicializaEstorno();
        parcelas[0].cod_tipo_indica_econom = "xxx";


        await WithUnitOfWorkAsync(async () =>
        {
            //_unitOfWorkManager.Begin(requiresNew: true, isTransactional: true).Returns(unitOfWork);
            _situacaoRequisicaoProtocoloRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<SituacaoRequisicaoProtocolo, bool>>>()).Returns(situacaoProtocolo);
            _reqPagUnitOfWork.RequisicaoEstornoRepository.BuscaPorRequis(Arg.Any<string>()).Returns(estornos);
            _reqPagUnitOfWork.RequisicaoProtocoloRepository.BuscaPorPropostaNaoImportadasPorProtocolo(Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>()).Returns(requisicoes[0]);
            _reqPagUnitOfWork.RequisPropostaParcelaRepository.BuscaPorRequisicaoLista(Arg.Any<string>()).Returns(parcelas);
            _assuntoRepository.ObterAssuntoImportarReqPagAsync(Arg.Any<string>()).Returns(assunto);

            _unidadeJudicialRepository.GetAsync(Arg.Any<Expression<Func<UnidadeJudicial, bool>>>()).Returns(Task.FromResult(unidadeJudicial));

            _verificacaoTipoRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<VerificacaoTipo, bool>>>()).Returns(verificacaoTipo);

            _tipoIndicadorEconomicoRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<IndicadorEconomicoTipo, bool>>>()).Returns(new IndicadorEconomicoTipo()
            {
                Codigo = requisicoes[0].cod_tipo_indica_econom,
                TipoIndicadorEconomicoId = 1
            });

            _tipoIndicadorEconomicoRepository
            .FirstOrDefaultAsync(Arg.Is<Expression<Func<IndicadorEconomicoTipo, bool>>>(expr => expr.Compile().Invoke(new IndicadorEconomicoTipo { Codigo = "xxx" })))
            .Returns((IndicadorEconomicoTipo)null);


            // Act  
            var queryable = new List<Proposta>()
                {
                    proposta
                }.AsQueryable();

            var buildMock = queryable.BuildMockDbSet();
            _propostaRepository.GetQueryableAsync().Returns(buildMock);

            _propostaRepository.PropostaComDetalhes().Returns(queryable);


            // Assert
            var exception = await Assert.ThrowsAsync<UserFriendlyException>(async () => await _manager.InserirImportacaoAsync(proposta.PropostaId, "12345"));
            Assert.Equal($"Não foi possível encontrar tipo de indicador econômico com código {parcelas[0].cod_tipo_indica_econom}.", exception.Message);
        });
    }
    #endregion

    #region criarObjs

    private static SincronizacaoLegado.Models.ReqPag.RequisicaoEstornoReqPag InicializaEstorno()
    {
        var fakers = new Bogus.Faker<SincronizacaoLegado.Models.ReqPag.RequisicaoEstornoReqPag>("pt_BR")
            .RuleFor(p => p.num_protoc_requis, p => p.Random.Hash(5))
            .RuleFor(p => p.num_requis, p => p.Random.String())
            .RuleFor(p => p.num_banco, p => p.Random.String())
            .RuleFor(p => p.num_conta_corren, p => p.Random.String())
            .RuleFor(p => p.nom_benefi, p => p.Random.String())
            .RuleFor(p => p.cod_benefi, p => p.Random.String())
            .RuleFor(p => p.dat_recolh_conta, p => DateTime.Now)
            .RuleFor(p => p.val_recolh_conta, p => p.Random.Decimal())
            .RuleFor(p => p.dat_hora_protoc_requis, p => DateTime.Now)
            .Generate();
        return fakers;
    }
    private static Assunto InicializaAssunto()
    {
        var assunto = new SISPREC.Assuntos.Assunto()
        {
            CodigoCJF = "001"
        };
        return assunto;
    }

    private static SituacaoRequisicaoProtocolo InicializaSituacaoProtocolo()
    {
        var situacaoProtocolo = new SituacaoRequisicaoProtocolo()
        {
            SituacaoRequisicaoProtocoloId = 1,
            DescricaoSituacao = "Teste"
        };

        return situacaoProtocolo;
    }

    private static Proposta CreateRandomProposta()
    {
        var unidade = new SISPREC.Unidades.Unidade
        {
            UnidadeId = 1,
            Nome = "Tribunal Regional Federal da 3ª Região",
            IsDeleted = false,
            NumeroCnpjCpf = "123456",
            CodigoTipoUnidade = ECodigoTipoUnidade.E,
        };
        var tipoProcedimento = new Bogus.Faker<SISPREC.TiposProcedimentos.TipoProcedimento>()
                .RuleFor(p => p.DescricaoTipoProcedimento, f => f.Random.String2(20))
                    .Generate();

        tipoProcedimento.TipoProcedimentoId = "RPV";

        var proposta = new Proposta
        {
            PropostaId = 1,
            AnoProposta = 2024,
            MesProposta = 1,
            DataAtualizacao = new DateTime(2024, 10, 3, 0, 0, 0, DateTimeKind.Local),
            ValorMinimo = 0,
            ValorMaximo = 50,
            ValorMinimoParcela = 0,
            QtdMaximaParcelaAlimetar = 0,
            QtdMaximaParcelaComum = 0,
            QtdMaximaParcelaDesapropriacaoUnico = 0,
            QtdMaximaParcelaDesapropriacao = 0,
            SituacaoProposta = ESituacaoProposta.PENDENTE,
            DataInicioCalculoJuros = new DateTime(2024, 10, 3, 0, 0, 0, DateTimeKind.Local),
            TipoProcedimentoId = tipoProcedimento.TipoProcedimentoId,
            TipoProcedimento = tipoProcedimento,
            Unidade = unidade
        };

        return proposta;
    }

    private static List<SincronizacaoLegado.Models.ReqPag.RequisicaoProtocoloImportacao> InicializarReqProcotoloNaoImportadas()
    {
        var requisicaoProcoloImportacao = new List<TRF3.SISPREC.SincronizacaoLegado.Models.ReqPag.RequisicaoProtocoloImportacao>()
            {

                new TRF3.SISPREC.SincronizacaoLegado.Models.ReqPag.RequisicaoProtocoloImportacao
                {
                    num_oficio_requit = "12345",
                    cod_siafi_juizo_origem = 301174,
                    sta_protoc_requis = "4",
                    dat_hora_protoc_requis = DateTime.Now,
                    ide_protoc_requis= "E",
                    ide_reincl = "O",
                    ide_estorn= "A",
                    cod_situac_requis_propos ="P",
                    cod_situac_requis= 1,
                    num_protoc_requis_unica="12345",
                    num_protoc_requis="12345",
                    ide_bloque_deposi_judici = "C",
                    ide_requis = "I",
                    ide_desiga_magist ="T",
                    ide_nature_credit= "C",
                    ind_juros_mora= "NAO",
                    ide_requis_honora ="C",
                    ide_propos_inicia ="N",
                    cod_tipo_indica_econom = "CJF"
                }
        };

        return requisicaoProcoloImportacao;
    }

    private static UnidadeJudicial InicializarUnidadeJudicial()
    {
        var unidadeJudicial = new UnidadeJudicial
        {
            CodigoSiafi = "ABC123",
            Descricao = "Unidade Judicial Teste",
            DataUtilizacaoInicio = new DateTime(2021, 01, 01, 0, 0, 0, DateTimeKind.Local),
            DataUtilizacaoFim = new DateTime(2023, 01, 01, 0, 0, 0, DateTimeKind.Local),
            DataRegistro = new DateTime(2020, 01, 01, 0, 0, 0, DateTimeKind.Local),
            Seq_Unidad_Judici = 1,
            SequencialCJF = 1
        };

        return unidadeJudicial;
    }

    private static VerificacaoTipo InicializarRequisicaoVerificacao()
    {
        var verificacaoTipo = new VerificacaoTipo()
        {
            DescricaoTipo = "VERIFICACAO CPF/CNPJ",
            Ativo = true,
            VerificacaoTipoId = 1
        };

        return verificacaoTipo;
    }

    private static List<SincronizacaoLegado.Models.ReqPag.RequisPropostaParcela> InicializaRequisicaoParcelaProtocoloReqPag()
    {
        var parcelas = new List<SincronizacaoLegado.Models.ReqPag.RequisPropostaParcela> {
            new SincronizacaoLegado.Models.ReqPag.RequisPropostaParcela
            {
                 num_protoc_requis = "12345",
                 val_juros_mora = 0,
                 cod_tipo_proced ="RPC",
                 dat_calcul_parcel = DateTime.Now,
                 num_propos = 1,
                 ano_propos = 2024,
                 cod_cadast_entida = 1050,
                 qtd_maxima_parcel = 99,
                 dat_indica_econom = DateTime.Now,
                 qtd_parcel = 1,
                 val_atuali_deduca_indivi = 0,
                 val_atuali_exerci_anteri = 0,
                 val_atuali_exerci_corren = 0,
                 val_atuali_requis_parte_pss = 0,
                 val_atuali_total = 100,
                 val_atuali_total_j = 0,
                 val_atuali_total_p = 0,
                 val_minimo_parcel = 0,
                 val_indica_econom = 0,
                 cod_tipo_indica_econom = "IPC"

            }
        };
        return parcelas;
    }
    #endregion

}
