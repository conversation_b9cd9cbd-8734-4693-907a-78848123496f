@page
@using TRF3.SISPREC.Web
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Menus
@model TRF3.SISPREC.Web.Pages.ModelosDocumentos.CreateModel
@inject IPageLayout PageLayout
@{
	PageLayout.Content.Title = "Inserir Modelo de Documento";
	PageLayout.Content.MenuItemName = SISPRECMenus.ModeloDocumento;
}

@section styles {
	<link href="../css/summernote/summernote-bs5.min.css" rel="stylesheet">
}
@section scripts {
	<abp-script-bundle name="@typeof(TRF3.SISPREC.Web.Pages.ModelosDocumentos.CreateModel).FullName">
		<abp-script src="/Pages/ModelosDocumentos/shared.js" />
		<abp-script src="/Pages/ModelosDocumentos/create.js" />
	</abp-script-bundle>
	<script src="../js/summernote/summernote-bs5.min.js"></script>
	<script src="../js/summernote/lang/summernote-pt-BR.min.js"></script>
}
<abp-card>
	<abp-card-body>
		<abp-dynamic-form abp-model="ViewModel" data-ajaxForm="true" id="ModeloDocumentoForm">
			<abp-form-content />
			<abp-row>
				<abp-column class="text-start">
					<abp-select asp-for="ViewModel.SetorId" asp-items="@Model.ViewModel.SetorLookupList" />
				</abp-column>
			</abp-row>
			<input type="hidden" asp-for="ViewModel.TextoDocumento" />
			<div id="editor"></div>
			<abp-button button-type="Outline_Primary" id="btnCancelar">Cancelar</abp-button>
			<abp-button button-type="Primary" id="btnSalvar">
				<i class="fa fa-check me-1"></i> Salvar
			</abp-button>
		</abp-dynamic-form>
	</abp-card-body>
</abp-card>