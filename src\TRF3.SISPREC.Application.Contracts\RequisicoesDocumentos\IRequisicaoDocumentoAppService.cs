using TRF3.SISPREC.RequisicoesDocumentos.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.RequisicoesDocumentos;

public interface IRequisicaoDocumentoAppService :
    ICrudAppService<
        RequisicaoDocumentoDto,
        long,
        RequisicaoDocumentoGetListInput,
        CreateUpdateRequisicaoDocumentoDto,
        CreateUpdateRequisicaoDocumentoDto>
{
    Task GerarEspelhoRequisicaoAsync(CreateEspelhoRequisicaoDto input);

    Task ExcluirEspelhoRequisicaoAsync(string numeroRequisicao);
}