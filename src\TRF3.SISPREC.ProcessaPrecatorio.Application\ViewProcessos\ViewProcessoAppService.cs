using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.FileUtils;
using TRF3.SISPREC.ViewProcessos.Dtos;

namespace TRF3.SISPREC.ViewProcessos;

public class ViewProcessoAppService : BaseReadOnlyAppService<ViewProcesso, ViewProcessoDto, int, ViewProcessoGetListInput>, IViewProcessoAppService
{
    protected IViewProcessoRepository _repository;
    private readonly IFileUtil _fileUtil;

    public ViewProcessoAppService(IViewProcessoRepository viewProcessoRepository, IFileUtil fileUtil) : base(viewProcessoRepository)
    {
        _repository = viewProcessoRepository;
        _fileUtil = fileUtil ?? throw new ArgumentException(nameof(fileUtil));
    }

    protected override async Task<ViewProcesso> GetEntityByIdAsync(int id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync((await _repository.WithDetailsAsync()).Where(e => e.FaseId == id));
    }

    protected override IQueryable<ViewProcesso> ApplyDefaultSorting(IQueryable<ViewProcesso> query)
    {
        return query.OrderBy(e => e.ControleId);
    }

    protected override async Task<IQueryable<ViewProcesso>> CreateFilteredQueryAsync(ViewProcessoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.ControleId > 0, x => x.ControleId == input.ControleId)
            .WhereIf(input.FaseId > 0, x => x.FaseId == input.FaseId)
            .WhereIf(!input.NumeroProcesso.IsNullOrWhiteSpace(), x => x.NumeroProcesso.Contains(input.NumeroProcesso))
            .WhereIf(input.Status != null, x => x.Status == input.Status.GetEnumDescription())
            ;
    }

    public async Task<ViewProcessoFileDto> BaixarJson(int controleId, string numeroProcesso)
    {

        var result = await _fileUtil.BuscarArquivoNaPasta(controleId.ToString(), numeroProcesso, Enums.ETipoEnvioCJF.Envio);

        return new ViewProcessoFileDto()
        {
            bytes = result.Item1,
            Nome = result.Item2
        };
    }

    public async Task<ViewProcessosGrupoStatusListDto> ListarStatus(ViewProcessoGetListInput input)
    {
        var query = await base.CreateFilteredQueryAsync(input);
        var groupedData = await query
            .WhereIf(input.ControleId > 0, x => x.ControleId == input.ControleId)
            .WhereIf(input.FaseId > 0, x => x.FaseId == input.FaseId)
            .WhereIf(!input.NumeroProcesso.IsNullOrWhiteSpace(), x => x.NumeroProcesso.Contains(input.NumeroProcesso))
            .WhereIf(input.Status != null, x => x.Status == input.Status.GetEnumDescription())
            .GroupBy(x => x.Status)
            .Select(x => new ViewProcessosGrupoStatusDto()
            {
                Status = x.Key,
                Quantidade = x.Count(),
            })
            .ToListAsync();

        return new ViewProcessosGrupoStatusListDto()
        {
            values = groupedData,
            Total = groupedData.Sum(x => x.Quantidade),
        };
    }
}
