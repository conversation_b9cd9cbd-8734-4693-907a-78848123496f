using Shouldly;
using TRF3.SISPREC.BlocosSisprec;
using TRF3.SISPREC.ExpedientesAdministrativos;
using TRF3.SISPREC.ExpedientesAdministrativos.Dtos;
using Volo.Abp;

namespace TRF3.SISPREC.EntityFrameworkCore.Applications
{
    public class ExpedienteAdministrativoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
    {
        private readonly IExpedienteAdministrativoAppService _service;
        private readonly IExpedienteAdministrativoRepository _repository;
        private readonly IBlocoSisprecRepository _blocoSisprecRepository;

        public ExpedienteAdministrativoAppServiceTests()
        {
            _service = GetRequiredService<IExpedienteAdministrativoAppService>();
            _repository = GetRequiredService<IExpedienteAdministrativoRepository>();
            _blocoSisprecRepository = GetRequiredService<IBlocoSisprecRepository>();

            #region Insert ExpedienteAdministrativo

            _repository.InsertAsync(new ExpedienteAdministrativo
            {
                ExpedienteAdministrativoId = 1,
                BlocoSisprec = null,
                BlocoSisprecId = null,
                DataExpedienteAdministrativo = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                IdentificadorProcessoSei = "123",
                NumeroExpedienteAdministrativo = 123,
                TipoProcessoSei = Enums.ETipoProcessoSei.EXPEDIENTE_ADMINISTRATIVO,
                NumeroProcessoSei = "123",
                StatusExpedienteAdminstrativo = Enums.EStatusAdministrativo.LIBERADO_CUMPRIMENTO,
                TipoExpedienteAdministrativo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO,
                ObservacaoExpedienteAdministrativo = "Teste"
            });

            _repository.InsertAsync(new ExpedienteAdministrativo
            {
                ExpedienteAdministrativoId = 2,
                BlocoSisprec = null,
                BlocoSisprecId = null,
                DataExpedienteAdministrativo = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                IdentificadorProcessoSei = "123",
                NumeroExpedienteAdministrativo = 123,
                TipoProcessoSei = Enums.ETipoProcessoSei.EXPEDIENTE_ADMINISTRATIVO,
                NumeroProcessoSei = "123",
                StatusExpedienteAdminstrativo = Enums.EStatusAdministrativo.LIBERADO_CUMPRIMENTO,
                TipoExpedienteAdministrativo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO,
                ObservacaoExpedienteAdministrativo = "Teste"
            });

            _repository.InsertAsync(new ExpedienteAdministrativo
            {
                ExpedienteAdministrativoId = 3,
                BlocoSisprec = null,
                BlocoSisprecId = null,
                DataExpedienteAdministrativo = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                IdentificadorProcessoSei = "123",
                NumeroExpedienteAdministrativo = 123,
                TipoProcessoSei = Enums.ETipoProcessoSei.EXPEDIENTE_ADMINISTRATIVO,
                NumeroProcessoSei = "123",
                StatusExpedienteAdminstrativo = Enums.EStatusAdministrativo.LIBERADO_CUMPRIMENTO,
                TipoExpedienteAdministrativo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO,
                ObservacaoExpedienteAdministrativo = "Teste"
            });

            #endregion

            #region Insert BlocoSisprec

            _blocoSisprecRepository.InsertAsync(new BlocoSisprec
            {
                BlocoSisprecId = 1,
                DataCriacao = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                StatusBloco = Enums.EStatusBloco.GERADO
            });

            _blocoSisprecRepository.InsertAsync(new BlocoSisprec
            {
                BlocoSisprecId = 2,
                DataCriacao = DateTime.Now,
                NomeUsuario = "Usuario Teste",
                StatusBloco = Enums.EStatusBloco.GERADO
            });

            #endregion
        }


        [Fact]
        public async Task GetListAsync_Deve_Retornar_Expedientes()
        {
            // Arrange
            var input = new ExpedienteAdministrativoGetListInput()
            {
                NumeroProcessoSei = "123",
                TipoExpedienteAdministrativo = null,
                DataExpedienteAdministrativo = null,
                NomeUsuario = null,
                ObservacaoExpedienteAdministrativo = null,
                StatusExpedienteAdminstrativo = null,
                BlocoSisprecId = null,
                NumeroRequisicao = null,
            };

            // Act
            var result = await _service.GetExpedientes(input);

            // Assert
            result.Items.ShouldContain(x => x.NumeroProcessoSei == "123");
        }

        [Fact]
        public async Task IncluirExpedienteEmBlocoExistente_Deve_Incluir_Corretamente()
        {
            //Arrange
            var blocoDto = new CreateSemBlocoDto
            {
                BlocoId = 1,
                ExpedienteId = new List<int> { 1, 2, 3 }
            };

            //Act
            await _service.IncluirExpedienteEmBlocoExistente(blocoDto);

            //Assert
            foreach (var expedienteId in blocoDto.ExpedienteId)
            {
                var expediente = await _repository.GetAsync(x => x.ExpedienteAdministrativoId == expedienteId);
                expediente.BlocoSisprecId.ShouldBe(blocoDto.BlocoId);
            }
        }

        [Fact]
        public async Task GerarBloco_Deve_Gerar_Corretamente()
        {
            //Arrange
            var blocosDto = new List<SemBlocosDto>
            {
                new SemBlocosDto
                {
                    Id = 1,
                    Tipo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO.ToString()
                },
                new SemBlocosDto
                {
                    Id = 2,
                    Tipo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO.ToString()
                }
            };
            //Act
            await _service.GerarBloco(blocosDto);

            //Assert
            foreach (var bloco in blocosDto)
            {
                var expediente = await _repository.GetAsync(x => x.ExpedienteAdministrativoId == bloco.Id);
                expediente.BlocoSisprecId.ShouldNotBeNull();
            }
        }

        [Fact]
        public async Task GerarBloco_Nao_Deve_Permitir_TipoExpedientes_Diferente()
        {
            // Arrange
            var blocosDto = new List<SemBlocosDto>
            {
                new SemBlocosDto
                {
                    Id = 1,
                    Tipo = Enums.ETipoExpedienteAdministrativo.ADITAMENTO.ToString()
                },
                new SemBlocosDto
                {
                    Id = 2,
                    Tipo = Enums.ETipoExpedienteAdministrativo.CANCELAMENTO.ToString()
                }
            };

            // Act
            var exception = await Record.ExceptionAsync(() => _service.GerarBloco(blocosDto));

            // Assert
            exception.ShouldBeOfType<UserFriendlyException>();

        }
    }
}