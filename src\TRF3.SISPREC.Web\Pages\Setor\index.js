$(function () {

    $("#SetorFilter :input").on('input', function () {
        dataTable.ajax.reload();
    });


    const getFilter = function () {
        const input = {};
        $("#SetorFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/SetorFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.setores.setor;
    const detalheModal = new abp.ModalManager(abp.appPath + 'Setor/DetalheModal');
    const createModal = new abp.ModalManager(abp.appPath + 'Setor/CreateModal');
    const editModal = new abp.ModalManager(abp.appPath + 'Setor/EditModal');

    const dataTable = $('#SetorTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: false,
        scrollCollapse: true,
        ajax: abp.libs.datatables.createAjax(service.getList,getFilter),
        columnDefs: [
            {
                rowAction: {
                    items:
                        [
                            {
                                text: "Detalhe",
                                action: function (data) {
                                    detalheModal.open({ id: data.record.setorId});
                                }
                            },
                            {
                                text: "Alterar",
                                action: function (data) {
                                    editModal.open({ id: data.record.setorId});
                                }
                            },
                            {
                                text: "Excluir",
                                confirmMessage: function (data) {
                                    return "Tem certeza de que deseja excluir?"
                                },
                                action: function (data) {
                                    abp.ui.block({ elm: 'body', busy: true });
                                    service.delete(data.record.setorId)
                                        .then(function () {
                                            abp.notify.success('Setor excluído com sucesso!');
                                        }).always(function () {
                                            dataTable.ajax.reload();
                                            abp.ui.unblock();
                                        });
                                }
                            }
                        ]
                }
            },
            {
                title: "Sigla",
                data: "sigla"
            },
            {
                title: "Descrição",
                data: "nome"
            },
            {
                title: "Ativo",
                data: "ativo",
                render: function (ativo, type, row, meta) {
                    if (ativo) {
                        return "Sim"
                    }

                    return "Não"
                }
            }
        ]
    }));

    createModal.onResult(function () {
        dataTable.ajax.reload();
        abp.notify.success('Setor cadastrado com sucesso!');
    });

    editModal.onResult(function () {
        dataTable.ajax.reload();
        abp.notify.success('Setor alterado com sucesso!');
    });

    $('#NewSetorButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });
});
