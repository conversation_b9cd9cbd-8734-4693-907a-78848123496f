using Shouldly;

namespace TRF3.SISPREC.Pages.ExpedientesAdministrativos
{
    public class CreateModalTests : SISPRECWebTestBase
    {
        [Fact]
        public async Task Index_Page_Test()
        {
            // Arrange
            string tipo = "cancelamento";
            string id = "1,2,3";

            string url = $"/ExpedientesAdministrativos/CreateModal?Tipo={tipo}&Id={id}";

            // Act
            var response = await GetResponseAsync(url);
            var responseString = await GetResponseAsStringAsync(url);

            // Assert
            response.ShouldNotBeNull();
            responseString.ShouldNotBeNull();
            response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);
        }
    }
}
