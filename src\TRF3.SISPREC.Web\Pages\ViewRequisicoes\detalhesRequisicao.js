import { formatarValorMonetario } from '/js/util.js';

$(function () {

    $("#btnPesquisar").on("click", function () {
        let numProtocRequis = $("#ConsultaModel_NumeroProtocoloRequisicao").val();
        window.location = "DetalhesRequisicao?numProtocRequis=" + numProtocRequis
    });

    const queryParams = new URLSearchParams(window.location.search);
    const numProtocRequisQueryString = queryParams.get("numProtocRequis");
    const serviceConsultaRequisicao = tRF3.sISPREC.requisicoesPropostas.consultaRequisicao;
    const numeroRequisicaoProtocolo = queryParams.get("numProtocRequis");

    const getFilter = function () {
        return numProtocRequisQueryString;
    };

    const dataTable = $("#ViewDetalhesRequisicaoOriginariosListagemTable").DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: false,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: true,
        scrollCollapse: false,
        order: [[0, "asc"]],
        ajax: abp.libs.datatables.createAjax(serviceConsultaRequisicao.getDetalhesRequisicaoOriginariosListagem, function () {
            return numProtocRequisQueryString;
        }),
        columnDefs: [
            {
                title: "Num_protoc_requis",
                data: "numeroProtocoloRequisicao"
            },
            {
                title: "Num_proces_origin",
                data: "numeroProcessoOriginario"
            },
            {
                title: "Cod Unidade",
                data: "unidadeJudicialId"
            },
            {
                title: "Dat_protoc_proces_origin",
                data: "dataProtocoloProcessoOriginal",
                render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss')
            },
            {
                title: "Cod_Siafi_Unidad",
                data: "unidadeJudicial.codigoSiafi"
            },
            {
                title: "Cod_Tipo_Juizo",
                data: "unidadeJudicial.unidadeJudicialOrigem.tipoJuizo"
            },
            {
                title: "Email",
                data: "unidadeJudicial.unidadeJudicialOrigem.email"
            },
            {
                title: "Juízo",
                data: "unidadeJudicial.descricao"
            }
        ]
    }));

    const dataTableExpedienteListagemTable = $("#ViewRequisicaoAutorExpedienteListagemTable").DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: false,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: true,
        scrollCollapse: false,
        order: [[0, "asc"]],
        ajax: abp.libs.datatables.createAjax(serviceConsultaRequisicao.getDetalhesRequisicaoAutorExpedienteListagem, function () {
            return { numeroRequisicaoProtocolo: numeroRequisicaoProtocolo };;
        }),
        columnDefs: [
            {
                title: "Nº Requisição",
                data: "numeroProtocoloRequisicao"
            },
            {
                title: "Nº Expediente",
                data: "numeroExpedienteAdministrativo"
            },
            {
                title: "Nº Petição Judicial",
                data: "numeroPeticaoJudiciario"
            },
            {
                title: "Nº Processo SEI",
                data: "numeroProcessoSei",
            },
            {
                title: "Data Expediente",
                data: "dataExpedienteAdministrativo",
                render: DataTable.render.datetime('DD/MM/YYYY')
            },
            {
                title: "Descrição Expediente",
                data: "descricaoObservacaoExpedienteAdministrativo"
            },
            {
                title: "Usuário",
                data: "nomeUsuario"
            },
            {
                title: "Tipo Expediente",
                data: "tipoExpedienteAdministrativo"
            }
        ]
    }));

    const dataTableRequerenteHonorarios = $("#ViewDetalhesRequisicaoRequerenteHonorariosListagemTable").DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: false,
        paging: false,
        searching: false,//disable default searchbox
        autoWidth: true,
        scrollCollapse: false,
        order: [[1, "asc"]],
        ajax: abp.libs.datatables.createAjax(serviceConsultaRequisicao.getDetalhesRequisicaoRequerenteHonorariosListagem, function () {
            return numProtocRequisQueryString;
        }),
        columnDefs: [
            {
                title: "ID Pessoa",
                data: "pessoaId",
            },
            {
                title: "Tipo",
                data: "tipo"
            },
            {
                title: "Nom pessoa",
                data: "nomePessoa"
            },
            {
                title: "Num cnpj cpf",
                data: "numeroCnpjCpf"
            },
            {
                title: "Ide Situac",
                data: "situacaoParte"
            },
            {
                title: "Val Requis Parte",
                data: "valorRequisicaoParte",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Val Requis P",
                data: "valorRequisicaoPartePrincipal",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Val Requis J",
                data: "valorRequisicaoParteJuros",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Val Atuali",
                data: "valorAtualizadoRequisicaoParte",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Val Atuali P",
                data: "valorAtualizadoRequisicaoPartePrincipal",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Val Atuali J",
                data: "valorAtualizacaoRequisicaoParteJuros",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Juros Mora",
                data: "valorJuros",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            }
        ]
    }));

    const dataTableRequisicaoDetalhesOriginarios = $("#RequisicaoPorOriginariosViewModelTable").DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: false,
        paging: true,
        searching: false,
        autoWidth: true,
        scrollCollapse: false,
        order: [[1, "asc"]],
        ajax: abp.libs.datatables.createAjax(serviceConsultaRequisicao.getRequisicaoOriginarios, function () {
            return { numeroRequisicaoProtocolo: numeroRequisicaoProtocolo };
        }),
        columnDefs: [
            {
                title: "Originário",
                data: "numeroProcessoOrigem",
            },
            {
                title: "Juízo de Origem",
                data: "descricaoUnidadeJudicial"
            },
            {
                title: "Nº Requisição",
                data: "numeroProtocoloRequisicao"
            },
            {
                title: "Procedimento",
                data: "codigoTipoProcedimento"
            },
            {
                title: "Unidade",
                data: "unidade"
            },
            {
                title: "Ano Proposta",
                data: "anoProposta"
            },
            {
                title: "Mês Proposta",
                data: "mesProposta"
            },
            {
                title: "Situação na Proposta",
                data: "situacaoRequisicaoProsta",
            },
            {
                title: "Código Requerente",
                data: "codigoPessoa"
            },
            {
                title: "Nome Requerente",
                data: "requerente"
            },
            {
                title: "CPF / CNPJ Requerente",
                data: "cpfRequerente"
            },
            {
                title: "Valor Requerente",
                data: "valorRequerente",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Código Contratual",
                data: "codigoPessoaHonorarioContratual"
            },
            {
                title: "Nome Contratual",
                data: "requerenteHonorarioContratual"
            },
            {
                title: "CPF / CNPJ Contratual",
                data: "cpfHonorarioContratual"
            },
            {
                title: "Valor Contratual",
                data: "valorHonorarioContratual",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
        ]
    }));

    let cpfRequerente = $("#cpfRequerenteInput").val();

    const dataTableInformacoesParcelas = $("#ViewDetalhesRequisicaoInformacoesParcelasTable").DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: false,
        paging: false,
        searching: false,//disable default searchbox
        autoWidth: true,
        scrollCollapse: false,
        order: [[1, "asc"]],
        ajax: abp.libs.datatables.createAjax(serviceConsultaRequisicao.getDetalhesRequisicaoInformacoesParcelas, function () {
            return numProtocRequisQueryString;
        }),
        columnDefs: [
            {
                title: "Tipo",
                data: "tipo",
            },
            {
                title: "Seq_Pessoa",
                data: "pessoaId"
            },
            {
                title: "Nom_pessoa",
                data: "nomePessoa"
            },
            {
                title: "Num_cnpj_cpf",
                data: "numeroCnpjCpf"
            },
            {
                title: "Val_Parcel_Atuali_Requer",
                data: "valorParcelaAtualizadaRequerente",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Val_Parcel_Atuali_Requer_P",
                data: "valorParcelaAtualizadaRequerenteP",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Val_Parcel_Atuali_Requer_J",
                data: "valorParcelaAtualizadaRequerenteJ",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Val_Juros_Mora",
                data: "valorJurosMora",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Num_Parcel",
                data: "numeroParcela"
            }
        ]
    }));

    const dataTableRequisicaoRequerente = $("#RequisicaoRequerenteViewModelTable").DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: false,
        paging: true,
        searching: false,
        autoWidth: true,
        scrollCollapse: false,
        order: [[1, "asc"]],
        ajax: abp.libs.datatables.createAjax(serviceConsultaRequisicao.getRequisicaoRequerente, function () {
            return cpfRequerente;
        }),
        columnDefs: [
            {
                title: "CPF/CNPJ",
                data: "numeroCnpjCpf",
            },
            {
                title: "Nome Pessoa",
                data: "nomePessoa"
            },
            {
                title: "Código Pessoa",
                data: "codigoPessoa"
            },
            {
                title: "Nº Requisição",
                data: "numeroProtocoloRequisicao"
            },
            {
                title: "Valor Requisição (parte)",
                data: "valorRequisicaoParte",
                render: function (data, type, row) {
                    if (type === 'display' || type === 'filter') {
                        return formatarValorMonetario(data);
                    }

                    return data;
                }
            },
            {
                title: "Procedimento",
                data: "codigoTipoProcedimento"
            },
            {
                title: "Unidade",
                data: "unidadeId"
            },
            {
                title: "Ano Proposta",
                data: "anoProposta"
            },
            {
                title: "Mês Proposta",
                data: "mesProposta"
            },
            {
                title: "Situação na Proposta",
                data: "situacaoRequisicaoProposta"
            },
            {
                title: "Tipo",
                data: "tipo"
            }
        ]
    }));

    document.querySelectorAll('#ConsultaModel_NumeroProtocoloRequisicao').forEach((el) => {
        el.addEventListener('input', () => {
            if ($('#ConsultaModel_NumeroProtocoloRequisicao').val().trim() === '')
                $('#btnPesquisar').prop('disabled', true);
            else
                $('#btnPesquisar').prop('disabled', false);
        });
    });

    $(document).ready(function () {
        $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
            const target = $(e.target).attr("href");

            retornaDadosAbaRequisitada(target);

            dataTable.columns.adjust().draw();
            dataTableRequerenteHonorarios.columns.adjust().draw();
            dataTableRequisicaoRequerente.columns.adjust().draw();
            dataTableRequisicaoDetalhesOriginarios.columns.adjust().draw();
            dataTableExpedienteListagemTable.columns.adjust().draw();
            dataTableInformacoesParcelas.columns.adjust().draw();
        });

        $('#ViewDetalhesRequisicaoRequerenteHonorariosListagemTable_wrapper .dataTable_footer').remove();
        $('#ViewDetalhesRequisicaoInformacoesParcelasTable_wrapper .dataTable_footer').remove();
    });

    function retornaDadosAbaRequisitada(target) {
        switch (target) {
            case "#autorExpediente":
                serviceConsultaRequisicao.getDetalhesRequisicaoAutorExpediente(numProtocRequisQueryString)
                    .then(data => {
                        $('#RequisicaoAutorExpediente_CodPessoaAutor').val(data.pessoaAutorId);
                        $('#RequisicaoAutorExpediente_Autor').val(data.autor);
                        $('#RequisicaoAutorExpediente_CpfAutor').val(data.cpfAutor);
                        $('#RequisicaoAutorExpediente_IdeSituacPessoaAutor').val(data.situacaoParte);
                        $('#RequisicaoAutorExpediente_NomSocial').val(data.nomeSocial);
                    });
                break;

            case "#requeridoCompensacao":
                serviceConsultaRequisicao.getDetalhesRequisicaoRequeridoCompensacao(numProtocRequisQueryString)
                    .then(data => {
                        $('#RequisicaoRequeridoCompensacaoViewModel_CodPessoaReu').val(data.pessoaIdReu);
                        $('#RequisicaoRequeridoCompensacaoViewModel_Reu').val(data.reu);
                        $('#RequisicaoRequeridoCompensacaoViewModel_TipoIndicador').val(data.tipoIndicador);
                        $('#RequisicaoRequeridoCompensacaoViewModel_FatorAtualizacao').val((data.fatorAtualizacao?.toString().replace('.', ',') || ''));
                        $('#RequisicaoRequeridoCompensacaoViewModel_IdeSituacPessoaReu').val(data.situacaoPartePessoaReu);
                        $('#RequisicaoRequeridoCompensacaoViewModel_CnpjReu').val(data.cnpjReu);
                        $('#RequisicaoRequeridoCompensacaoViewModel_NumBanco').val(data.numeroBanco);
                        $('#RequisicaoRequeridoCompensacaoViewModel_NumAgenci').val(data.numeroAgencia);
                        $('#RequisicaoRequeridoCompensacaoViewModel_NumContaCorrent').val(data.numeroContaCorrente);
                        $('#RequisicaoRequeridoCompensacaoViewModel_CodElemenDespes').val(data.elementoDespesa);
                        $('#RequisicaoRequeridoCompensacaoViewModel_TipoDespesa').val(data.tipoDespesa);
                        $('#RequisicaoRequeridoCompensacaoViewModel_CodTipoSenten').val(data.tipoSentenca);
                        $('#RequisicaoRequeridoCompensacaoViewModel_IdeSituacRequisPropos').val(data.situacaoRequisicaoProposta);
                        $('#RequisicaoRequeridoCompensacaoViewModel_CodAdvogaReu').val(data.advogadoIdReu);
                        $('#RequisicaoRequeridoCompensacaoViewModel_AdvReu').val(data.advogadoReu);
                        $('#RequisicaoRequeridoCompensacaoViewModel_CpfAdvReu').val(data.cpfAdvReu);
                        $('#RequisicaoRequeridoCompensacaoViewModel_OabAdvReu').val(data.oabAdvogadoReu);
                        $('#RequisicaoRequeridoCompensacaoViewModel_StatusAdvogado').val(data.statusAdvogado);
                        $('#RequisicaoRequeridoCompensacaoViewModel_AdvNomeSocial').val(data.advogadoNomeSocial);
                        $('#RequisicaoRequeridoCompensacaoViewModel_DatIntimaReuFormatado').val(data.dataIntimaReu && !isNaN(new Date(data.dataIntimaReu))
                            ? new Date(data.dataIntimaReu).toLocaleDateString('pt-BR')
                            : '');
                    });
                break;

            case "#originarios":
                serviceConsultaRequisicao.getDetalhesRequisicaoOriginarios(numProtocRequisQueryString)
                    .then(data => {
                        $('#RequisicaoOriginariosViewModel_CodPessoaJuizo').val(data.requisicaoProtocolo.unidadeJudicialDto.seq_Unidad_Judici);
                        $('#RequisicaoOriginariosViewModel_Email').val(data.requisicaoProtocolo.unidadeJudicialDto.unidadeJudicialOrigem.email);
                        $('#RequisicaoOriginariosViewModel_CodSiafiUnidad').val(data.requisicaoProtocolo.unidadeJudicialDto.codigoSiafi);
                        $('#RequisicaoOriginariosViewModel_Juizo').val(data.requisicaoProtocolo.unidadeJudicialDto.descricao);
                    });
                break;

            case "#irPss":
                serviceConsultaRequisicao.getDetalhesRequisicaoIrPss(numProtocRequisQueryString)
                    .then(data => { 
                        $('#RequisicaoIrPssViewModel_CodPessoaRequerente').val(data.pessoaIdRequerente);
                        $('#RequisicaoIrPssViewModel_Requerente').val(data.requerente);
                        $('#RequisicaoIrPssViewModel_CpfRequerente').val(data.cpfRequerente);
                        $('#RequisicaoIrPssViewModel_NumMesesExerciAnteri').val(data.numeroMesesExercicioAnterior || 0);
                        $('#RequisicaoIrPssViewModel_ValExerciAnteriFormatado').val(formatarValorMonetario(data.valorExercicioAnterior));
                        $('#RequisicaoIrPssViewModel_ValAtualiExerciAnteriFormatado').val(formatarValorMonetario(data.valorAtualizadoExercicioAnterior));
                        $('#RequisicaoIrPssViewModel_ValDeducaIndiviFormatado').val(formatarValorMonetario(data.valorDeducaoIndividual));
                        $('#RequisicaoIrPssViewModel_ValAtualiDeducaIndiviFormatado').val(formatarValorMonetario(data.valorAtualizadoDeducaoIndividual));
                        $('#RequisicaoIrPssViewModel_NumMesesExerciCorren').val(data.numeroMesesExercicioCorrente || 0);
                        $('#RequisicaoIrPssViewModel_ValExerciCorrenFormatado').val(formatarValorMonetario(data.valorExercicioCorrente));
                        $('#RequisicaoIrPssViewModel_ValAtualiExerciCorrenFormatado').val(formatarValorMonetario(data.valorAtualizadoExercicioCorrente));
                        $('#RequisicaoIrPssViewModel_AnoExerciCorren').val(data.anoExercicioCorrente || 0);

                        $('#RequisicaoIrPssViewModel_ValDeducaPropostaFormatado').val(formatarValorMonetario(data.valorDeducaoProposta));
                        $('#RequisicaoIrPssViewModel_ValExecAntPropostaFormatado').val(formatarValorMonetario(data.valorExercicioAnteriorProposta));
                        $('#RequisicaoIrPssViewModel_ValExecCorrenPropostaFormatado').val(formatarValorMonetario(data.valorExercicioCorrenteProposta)); 

                        $('#RequisicaoIrPssViewModel_CodSiafi').val(data.codSiafi);
                        $('#RequisicaoIrPssViewModel_NomeUnidade').val(data.nomeUnidade);
                        $('#RequisicaoIrPssViewModel_IdeCondicServid').val(data.ideCondicServid);
                        $('#RequisicaoIrPssViewModel_ValRequisPartePssFormatado').val(formatarValorMonetario(data.valRequisPartePss));
                        $('#RequisicaoIrPssViewModel_ValAtualiRequisPartePssFormatado').val(formatarValorMonetario(data.valAtualiRequisPartePss));
                        $('#RequisicaoIrPssViewModel_ValorAtualiRequisPartePssFormatado').val(formatarValorMonetario(data.valorAtualiRequisPartePss));
                    });
                break;

            case "#ocorrencias":
                $("#ViewRequisicaoOcorrenciasTable").DataTable(abp.libs.datatables.normalizeConfiguration({
                    destroy: true,
                    processing: true,
                    serverSide: true,
                    paging: true,
                    searching: false,
                    autoWidth: true,
                    scrollCollapse: false,
                    order: [[2, "asc"]],
                    ajax: abp.libs.datatables.createAjax(serviceConsultaRequisicao.getDetalhesRequisicaoOcorrencias, () => ({
                        numeroRequisicaoProtocolo: numeroRequisicaoProtocolo
                    })),
                    columnDefs: [
                        { title: "N° Requisição", data: "numeroProtocoloRequisicao" },
                        { title: "Código Motivo", data: "codigoMotivoOcorrencia" },
                        { title: "Data Ocorrência", data: "dataRequisicaoOcorrencia", render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss') },
                        { title: "Usuário", data: "nomeUsuario" },
                        { title: "Tipo Ação", data: "codigoAcaoTipo" },
                        { title: "Descrição", data: "descricaoObservacao" },
                    ]
                }));
                break;

            case "#consultaCpf":
                $("#ViewRequisicaoConsultaCpfTable").DataTable(abp.libs.datatables.normalizeConfiguration({
                    destroy: true,
                    processing: true,
                    serverSide: true,
                    paging: false,
                    searching: false,
                    autoWidth: true,
                    scrollCollapse: false,
                    order: [[0, "desc"]],
                    ajax: abp.libs.datatables.createAjax(serviceConsultaRequisicao.getDetalhesRequisicaoConsultaCpf, () => ({
                        numeroRequisicaoProtocolo: numeroRequisicaoProtocolo
                    })),
                    columnDefs: [
                        { title: "TIPO PARTE", data: "tipoParte" },
                        { title: "NOME", data: "nome" },
                        { title: "CPF/CNPJ", data: "cpfCnpj" },
                        { title: "NOME NA RECEITA", data: "nomeReceita" },
                        { title: "DATA NASCIMENTO NA RECEITA", data: "dataNascimentoReceita", render: DataTable.render.datetime('DD/MM/YYYY') },
                        { title: "SITUAÇÃO CADASTRAL", data: "situacaoCadastral" },
                        { title: "DATA VERIFICAÇÃO", data: "dataVerificacao", render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss') },
                        {
                            title: "DIVERGÊNCIA", data: "divergencia", render: function (divergencia) {
                                if (divergencia == null)
                                    return ""

                                if (divergencia)
                                    return "Sim"

                                return "Não"
                            }
                        },
                        { title: "ERRO", data: "erro" }
                    ]
                }));
                break;
            case '#referReinclusao':

                $("#tableReferencias").DataTable(abp.libs.datatables.normalizeConfiguration({
                    destroy: true,
                    processing: true,
                    serverSide: true,
                    paging: false,
                    searching: false,
                    autoWidth: true,
                    scrollCollapse: false,
                    order: [[0, "desc"]],
                    ajax: abp.libs.datatables.createAjax(serviceConsultaRequisicao.getRequisicaoReferencias, () => ({
                        numeroRequisicaoProtocolo: numeroRequisicaoProtocolo
                    })),
                    columnDefs: [
                        { title: "NOME REQUERENTE", data: "nomePessoaRequerente" },
                        { title: "CPF/CNPJ REQUERENTE", data: "cpfCnpjRequerente" },
                        { title: "NOME REFERÊNCIA", data: "nomePessoaReferencia" },
                        { title: "CPF/CNPJ REFERÊNCIA", data: "cpfCnpjReferencia" },
                        {
                            title: "VALOR REFERÊNCIA", data: "valorRequisReferencia",
                            render: function (data, type, row) {
                                    return formatarValorMonetario(data);
                                }
                        },
                        {
                            title: "VALOR TOTAL REFERÊNCIA", data: "valorTotalReferencia",
                            render: function (data, type, row) {
                                    return formatarValorMonetario(data);
                                }
                            }
                    ]
                }));

                $("#tableReinclusao").DataTable(abp.libs.datatables.normalizeConfiguration({
                    destroy: true,
                    processing: true,
                    serverSide: true,
                    paging: false,
                    searching: false,
                    autoWidth: true,
                    scrollCollapse: false,
                    order: [[0, "desc"]],
                    ajax: abp.libs.datatables.createAjax(serviceConsultaRequisicao.getRequisicaoReinclusao, () => ({
                        numeroRequisicaoProtocolo: numeroRequisicaoProtocolo
                    })),
                    columnDefs: [
                        { title: "REQUISIÇÃO ESTORNADA", data: "numeroProtocoloRequisicao" },
                        { title: "BANCO", data: "numeroBanco" },
                        { title: "CONTA CORRENTE", data: "numeroContaCorrente" },
                        { title: "BENEFICIÁRIO", data: "nomeBeneficiario" },
                        { title: "CPF/CNPJ BENEFICIÁRIO", data: "codigoBeneficiario" },
                        {
                            title: "DATA ESTORNO", data: "dataRecolhidoConta", render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss')
                        },
                        {
                            title: "VALOR", data: "valorRecolhidoConta",
                            render: function (data, type, row) {
                                    return formatarValorMonetario(data);
                            }
                        },
                        {
                            title: "DATA PROTOCOLO", data: "dataHoraProtocRequisicao", render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss')
                        },
                    ]
                }));

                break;

            default:
                console.warn("Aba não reconhecida:", target);
                break;
        }
    }


    downloadPDF('btnExportarPDF', 'extrato-'.concat(numProtocRequisQueryString), serviceConsultaRequisicao, getFilter);

});