@page
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos
@using TRF3.SISPREC.Web.Menus
@model SemBlocoModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Expediente Administrativos Sem Bloco";
    PageLayout.Content.MenuItemName = SISPRECMenus.ExpedienteAdministrativoSemBloco;
}

@section scripts
{
    <abp-script src="/js/componente-utils.js" />
    <abp-script src="/Pages/ExpedientesAdministrativos/semBloco.js" />
    <abp-script src="/js/util.js" />
}

<abp-card>
    <abp-card-header>
        <form asp-for="ExpedienteAdministrativoFilter" id="ExpedienteAdministrativoFilter">
            <abp-row class="d-flex justify-content-between align-items-center">
                <abp-column>
                    <abp-input asp-for="ExpedienteAdministrativoFilter.NumeroProcessoSei" />
                </abp-column>
                <abp-column>
                    <abp-select asp-for="ExpedienteAdministrativoFilter.TipoExpedienteAdministrativo" asp-items="@Model.ExpedienteAdministrativoFilter.TipoExpedienteLookupList"/>
                </abp-column>
                <abp-column>
                    <abp-input asp-for="ExpedienteAdministrativoFilter.NomeUsuario" />
                </abp-column>
                <abp-column>
                    <input-requisicao-pesquisa asp-for="ExpedienteAdministrativoFilter.NumeroRequisicao" />
                </abp-column>
            </abp-row>

            <abp-row>
                <abp-column>
                    <abp-button class="float-start" button-type="Primary" text="Pesquisar" size="Small" id="btnPesquisar"></abp-button>
                </abp-column>
            </abp-row>
        </form>
    </abp-card-header>
    <abp-card-body>
        <abp-table striped-rows="true" id="ExpedienteAdministrativoTable" class="nowrap" />
        <abp-row class="mt-3">
            <abp-column class="text-end">
                <abp-button id="gerar-bloco" text="Gerar bloco" button-type="Outline_Primary" size="Medium" />
                <abp-button id="incluir-bloco" text="Incluir em Bloco Existente" button-type="Outline_Primary" size="Medium" />
            </abp-column>
        </abp-row>
    </abp-card-body>
</abp-card>

