@page
@using TRF3.SISPREC.Permissoes
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.ConsultarJustificativa
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@inject IAuthorizationService Authorization
@{
    PageLayout.Content.Title = "Consulta de Justificativa";
    PageLayout.Content.MenuItemName = SISPRECMenus.ConsultarJustificativa;
}

@section scripts
{
    <abp-script src="/js/util.js" />
    <abp-script src="/Pages/ConsultarJustificativa/index.js" />
}
@section styles
{
    <abp-style src="/Pages/ConsultarJustificativa/index.css" />
}

<abp-card>
    <abp-card-body>
        <form asp-for="ConsultaFilter" id="ConsultaFilter">
            <abp-row>
                <abp-column>
                    <abp-select asp-for="ConsultaFilter.TipoProcedimento" />
                </abp-column>
                <abp-column>
                    <input-requisicao-pesquisa asp-for="ConsultaFilter.NumeroRequisicao" />
                </abp-column>
                <abp-column>
                    <abp-select asp-for="ConsultaFilter.AnaliseTelaId" asp-items="@Model.ConsultaFilter.AnaliseTelaLookupList" />
                </abp-column>
                <abp-column>
                    <abp-input asp-for="ConsultaFilter.Usuario" />
                </abp-column>
                <abp-column>
                    <abp-date-picker asp-for="ConsultaFilter.DataInicio" />
                </abp-column>
                <abp-column>
                    <abp-date-picker asp-for="ConsultaFilter.DataTermino" />
                </abp-column>
                <abp-column>
                    <abp-select asp-for="ConsultaFilter.Decisao" />
                </abp-column>
                <abp-column style="align-content:center; text-align: end;">
                    <abp-button size="Small" button-type="Primary" id="btnPesquisar">
                        Pesquisar
                    </abp-button>
                </abp-column>
            </abp-row>
        </form>
        <abp-row>
            <abp-table striped-rows="true" id="ConsultaJustificativaTable" class="nowrap" />
        </abp-row>
        <br />
        <form method="post" id="formSalvar">
            <input asp-for="ViewModel.RequisicaoJustificativaId" hidden="hidden" />
            <abp-row>
                <abp-column size="_6">
                    <abp-row>
                        <abp-column>
                            <abp-input asp-for="ViewModel.Procedimento" readonly="true" />
                        </abp-column>
                        <abp-column>
                            <abp-input asp-for="ViewModel.NumeroProtocoloRequisicao" readonly="true" />
                        </abp-column>
                        <abp-column>
                            <abp-input asp-for="ViewModel.NomeUsuario" readonly="true" />
                        </abp-column>
                        <abp-column>
                            <abp-input asp-for="ViewModel.DataAnalise" readonly="true" />
                        </abp-column>
                    </abp-row>
                    <abp-row>
                        <abp-column size="_3">
                            <abp-select asp-for="ViewModel.Decisao" asp-items="@Model.ViewModel.DecisaoList" disabled="disabled" required-symbol=false />
                        </abp-column>
                        <abp-column>
                            <abp-select asp-for="ViewModel.Motivo" asp-items="@Model.ViewModel.MotivoList" disabled="disabled" />
                        </abp-column>
                    </abp-row>
                    <abp-row>
                        <abp-column size="_3">
                            <abp-input asp-for="ViewModel.TipoAnalise" readonly="true" />
                        </abp-column>
                        <abp-column>
                            <abp-input asp-for="ViewModel.ComplementoMotivo" readonly="true" />
                        </abp-column>
                    </abp-row>
                    <abp-row>
                        <abp-input asp-for="ViewModel.Observacoes" readonly="true" />
                    </abp-row>
                </abp-column>
                <abp-column>
                    <abp-row>
                        <abp-row>
                            <label class="form-label">Requisições Comparadas</label>
                        </abp-row>
                        <abp-column class="col-3">
                            <input type="text" id="newItemText" placeholder="nº de requisição" class="form-control newItemInput" readonly />
                        </abp-column>
                        <abp-column>
                            <input type="text" id="newItemTextObservacao" placeholder="observações para geração do espelho" class="form-control newItemInput" readonly />
                        </abp-column>
                        <abp-column class="col-2">
                            <div id="controls" style="text-align:right">
                                <button id="addButton" type="button" class="btn btn-primary btn-sm" style="display:none">+</button>
                                <button id="deleteButton" type="button" class="btn btn-primary btn-sm" style="display:none">-</button>
                            </div>
                        </abp-column>
                    </abp-row>
                    <abp-row>
                        <abp-column class="table-wrapper">
                            <br />
                            <abp-table striped-rows="true">
                                <thead>
                                    <tr>
                                        <th id="numero-requisicao" class="col-3">Nº Requisição</th>
                                        <th id="observacao">Observações</th>
                                    </tr>
                                </thead>
                                <tbody id="requisicoes-comparadas-table">
                                </tbody>
                            </abp-table>
                        </abp-column>
                    </abp-row>
                    <div id="requisicoesComparadas"></div>
                </abp-column>
            </abp-row>
        </form>
        <br />
        <abp-row>
            <abp-column style="text-align:right">
                <abp-button size="Medium" button-type="Primary" id="excluir" style="display:none">
                    Excluir
                </abp-button>
                <abp-button size="Medium" button-type="Primary" id="salvar" style="display:none">
                    Salvar
                </abp-button>
            </abp-column>
        </abp-row>
    </abp-card-body>
</abp-card>