@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.Peritos
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Perito Autorizado";
    PageLayout.Content.BreadCrumb.Add("Peritos");
    PageLayout.Content.MenuItemName = SISPRECMenus.Perito;
}

@section scripts
{
    <abp-script src="/js/util.js" />
    <abp-script src="/libs/jquery-mask-plugin/jquery.mask.js" />
    <abp-script src="/Pages/Peritos/index.js" />
    <abp-script src="/Pages/Peritos/createModal.js" />    
}

<abp-card>
    <abp-card-header>
        <abp-row class="justify-content-between align-items-center">
            <abp-column>
                <a abp-collapse-id="PeritoCollapse" class="text-secondary">Filtrar</a>
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="NewPeritoButton"
                            text="Novo"
                            icon="plus"
                            button-type="Primary" />
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <abp-dynamic-form abp-model="PeritoFilter" id="PeritoFilter" required-symbols="false" column-size="_3">
            <abp-collapse-body id="PeritoCollapse">
                <abp-form-content />
            </abp-collapse-body>
        </abp-dynamic-form>
        <abp-table striped-rows="true" id="PeritoTable" class="nowrap" />
    </abp-card-body>
</abp-card>
