using AutoMapper;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.ViewControles;
using TRF3.SISPREC.ViewControles.Dtos;
using TRF3.SISPREC.ViewFases;
using TRF3.SISPREC.ViewFases.Dtos;
using TRF3.SISPREC.ViewProcessos;
using TRF3.SISPREC.ViewProcessos.Dtos;
namespace TRF3.SISPREC;


[ExcludeFromCodeCoverage]
public class SISPRECProcessaPrecatorioApplicationAutoMapperProfile : Profile
{
    public SISPRECProcessaPrecatorioApplicationAutoMapperProfile()
    {
        CreateMap<ViewFase, ViewFaseDto>();
        CreateMap<ViewControle, ViewControleDto>();
        CreateMap<ViewProcesso, ViewProcessoDto>();
        CreateMap<ReprocessarDto, ViewControle>();
    }
}
