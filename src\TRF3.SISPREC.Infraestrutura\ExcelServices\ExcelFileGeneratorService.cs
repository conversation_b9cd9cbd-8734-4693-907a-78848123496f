using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System.ComponentModel;
using System.Globalization;
using System.Reflection;
using TRF3.SISPREC.Enums;
using Volo.Abp;

namespace TRF3.SISPREC.ExcelServices
{
    public class ExcelFileGeneratorService : IExcelFileGeneratorService
    {
        #region IExcelFileGeneratorService Members

        public byte[] GenerateExcelFile<TEntity, TFilter>(IList<TEntity> entities, EStyleFile style, TFilter? filtro = null)
            where TEntity : class
            where TFilter : class
        {
            using var memoryStream = new MemoryStream();
            using var document = SpreadsheetDocument.Create(memoryStream, SpreadsheetDocumentType.Workbook);

            var workbookPart = document.AddWorkbookPart();
            workbookPart.Workbook = new Workbook();

            var worksheetPart = workbookPart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());

            var sheets = workbookPart.Workbook.AppendChild(new Sheets());
            sheets.AppendChild(new Sheet
            {
                Id = workbookPart.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "Planilha 1"
            });

            var sheetData = worksheetPart.Worksheet.GetFirstChild<SheetData>();

            if (entities == null || !entities.Any())
                throw new UserFriendlyException("A lista de entidades não pode ser vazia.");

            if (filtro != null)
                GerarFiltro(filtro, sheetData);

            var properties = typeof(TEntity).GetProperties();


            var stylesPart = workbookPart.AddNewPart<WorkbookStylesPart>();
            stylesPart.Stylesheet = CreateStyleSheet();
            stylesPart.Stylesheet.Save();

            // Adiciona cabeçalho
            var headerRow = new Row();
            foreach (var prop in properties)
            {
                var descriptionAttribute = prop.GetCustomAttribute<DisplayNameAttribute>();
                string headerText = !string.IsNullOrEmpty(descriptionAttribute?.DisplayName) ? descriptionAttribute.DisplayName : prop.Name;

                var cell = new Cell
                {
                    DataType = GetExcelValues(prop),
                    CellValue = new CellValue(headerText?.ToString() ?? string.Empty),
                    StyleIndex = (uint)style,
                };
                headerRow.AppendChild(cell);
            }
            sheetData.AppendChild(headerRow);

            // Adiciona valores das entidades
            foreach (var entity in entities)
            {
                var dataRow = new Row();
                foreach (var prop in properties)
                {
                    string value = string.Empty;
                    var propValue = prop.GetValue(entity);
                    value = NormalizarCampos(propValue);

                    var cell = new Cell
                    {
                        DataType = GetExcelValues(prop),
                        CellValue = new CellValue(value?.ToString() ?? string.Empty),
                        StyleIndex = style == EStyleFile.TITULO_NEGRITO ? 0 : (uint)style
                    };
                    dataRow.AppendChild(cell);
                }
                sheetData.AppendChild(dataRow);
            }

            workbookPart.Workbook.Save();
            document.Dispose();
            return memoryStream.ToArray();
        }


        #endregion

        #region Private Methods

        private static string NormalizarCampos(object? propValue)
        {
            string value;
            if (propValue is decimal decimalValue)
                value = decimalValue.ToString("#,##0.00", new CultureInfo("pt-BR"));

            else if (propValue is bool boolValue)
            {
                if (boolValue)
                    value = "Sim";
                else
                    value = "Não";
            }

            else
                value = propValue?.ToString() ?? string.Empty;
            return value;
        }
        private void GerarFiltro<TFilter>(TFilter? filtro, SheetData? sheetData) where TFilter : class
        {
            var filtroProps = typeof(TFilter).GetProperties();

            foreach (var prop in filtroProps)
            {
                var headerText = ObterTextoCabecalho(prop, filtro);

                if (!OcultarTextoCabecalho(prop, filtro)) AdicionarLinhaNaSheet(sheetData, headerText);
            }

            AdicionarLinhaEmBranco(sheetData);
        }
        private bool OcultarTextoCabecalho(PropertyInfo prop, object? filtro)
        {
            var valorFormatado = ObterValorFormatado(prop, filtro);

            return string.IsNullOrEmpty(valorFormatado);
        }

        private string ObterTextoCabecalho(PropertyInfo prop, object? filtro)
        {
            var descriptionAttribute = prop.GetCustomAttribute<DisplayNameAttribute>();
            var valorFormatado = ObterValorFormatado(prop, filtro);

            return !string.IsNullOrEmpty(descriptionAttribute?.DisplayName)
                ? $"{descriptionAttribute.DisplayName} : {valorFormatado}"
                : $"{prop.Name} : {valorFormatado}";
        }

        private string? ObterValorFormatado(PropertyInfo prop, object? filtro)
        {
            var valor = prop.GetValue(filtro);

            if (prop.PropertyType == typeof(DateTime?) || prop.PropertyType == typeof(DateTime))
            {
                return valor != null ? ((DateTime)valor).ToString("dd/MM/yyyy") : string.Empty;
            }

            if (prop.PropertyType == typeof(bool?) || prop.PropertyType == typeof(bool))
            {
                if (valor == null) return null;
                return (bool)valor ? "Sim" : "Não";
            }

            return valor?.ToString() ?? string.Empty;
        }

        private void AdicionarLinhaNaSheet(SheetData? sheetData, string? texto)
        {
            var linha = new Row();
            var celula = new Cell
            {
                DataType = GetExcelValues(texto),
                CellValue = new CellValue(texto ?? string.Empty),
                StyleIndex = 8
            };
            linha.AppendChild(celula);
            sheetData?.AppendChild(linha);
        }

        private void AdicionarLinhaEmBranco(SheetData? sheetData)
        {
            sheetData?.AppendChild(new Row());
        }

        private CellValues GetExcelValues(object value)
        {
            if (value is null)
                return CellValues.String;

            Type type = value.GetType();

            return type switch
            {
                Type t when t == typeof(string) => CellValues.String,
                Type t when t == typeof(DateTime) => CellValues.Date,
                Type t when t == typeof(bool) => CellValues.Boolean,
                Type t when t == typeof(double) => CellValues.Number,
                Type t when t == typeof(decimal) => CellValues.Number,
                Type t when t == typeof(int) => CellValues.Number,
                Type t when t == typeof(long) => CellValues.Number,
                _ => CellValues.String
            };
        }
        private Stylesheet CreateStyleSheet()
        {
            Stylesheet stylesheet = new Stylesheet();
            #region Number format
#pragma warning disable IDE1006 // Naming Styles
            uint DATETIME_FORMAT = 164;
            uint DIGITS4_FORMAT = 165;
#pragma warning restore IDE1006 // Naming Styles
            var numberingFormats = new NumberingFormats();

            numberingFormats.Append(
                new NumberingFormat
                {
                    NumberFormatId = UInt32Value.FromUInt32(DATETIME_FORMAT),
                    FormatCode = StringValue.FromString("dd/mm/yyyy hh:mm:ss")
                },
                new NumberingFormat // 
                {
                    NumberFormatId = UInt32Value.FromUInt32(DIGITS4_FORMAT),
                    FormatCode = StringValue.FromString("0.00,00")
                }
            );
            numberingFormats.Count = UInt32Value.FromUInt32((uint)numberingFormats.ChildElements.Count);
            #endregion

            #region Fonts
            var fonts = new Fonts();
            fonts.Append(
                new Font()
                {
                    FontName = new FontName { Val = StringValue.FromString("Calibri") },
                    FontSize = new FontSize { Val = DoubleValue.FromDouble(11) }
                },
                new Font()
                {
                    FontName = new FontName { Val = StringValue.FromString("Arial") },
                    FontSize = new FontSize { Val = DoubleValue.FromDouble(11) },
                    Bold = new Bold()
                }
            );
            fonts.Count = UInt32Value.FromUInt32((uint)fonts.ChildElements.Count);
            #endregion

            #region Fills
            var fills = new Fills();
            fills.Append(
                 new Fill()
                 {
                     PatternFill = new PatternFill { PatternType = PatternValues.None }
                 },
                 new Fill()
                 {
                     PatternFill = new PatternFill { PatternType = PatternValues.Gray125 }
                 },
                 new PatternFill
                 {
                     PatternType = PatternValues.Solid,
                     ForegroundColor = TranslateForeground(System.Drawing.Color.LightBlue),
                     BackgroundColor = new BackgroundColor { Rgb = TranslateForeground(System.Drawing.Color.LightBlue).Rgb }
                 },
                 new PatternFill
                 {
                     PatternType = PatternValues.Solid,
                     ForegroundColor = TranslateForeground(System.Drawing.Color.LightSkyBlue),
                     BackgroundColor = new BackgroundColor { Rgb = TranslateForeground(System.Drawing.Color.LightBlue).Rgb }
                 }
             );
            fills.Count = UInt32Value.FromUInt32((uint)fills.ChildElements.Count);
            #endregion

            #region Borders
            var borders = new Borders();
            borders.Append(
                new Border
                {
                    LeftBorder = new LeftBorder(),
                    RightBorder = new RightBorder(),
                    TopBorder = new TopBorder(),
                    BottomBorder = new BottomBorder(),
                    DiagonalBorder = new DiagonalBorder()
                },
                new Border
                {
                    LeftBorder = new LeftBorder { Style = BorderStyleValues.Thin },
                    RightBorder = new RightBorder { Style = BorderStyleValues.Thin },
                    TopBorder = new TopBorder { Style = BorderStyleValues.Thin },
                    BottomBorder = new BottomBorder { Style = BorderStyleValues.Thin },
                    DiagonalBorder = new DiagonalBorder()
                },
                new Border
                {
                    LeftBorder = new LeftBorder(),
                    RightBorder = new RightBorder(),
                    TopBorder = new TopBorder { Style = BorderStyleValues.Thin },
                    BottomBorder = new BottomBorder { Style = BorderStyleValues.Thin },
                    DiagonalBorder = new DiagonalBorder()
                }
            );
            borders.Count = UInt32Value.FromUInt32((uint)borders.ChildElements.Count);
            #endregion

            #region Cell Style Format
            var cellStyleFormats = new CellStyleFormats();
            cellStyleFormats.Append(new CellFormat  // Cell style format index 0: no format
            {
                NumberFormatId = 0,
                FontId = 0,
                FillId = 0,
                BorderId = 0,
                FormatId = 0
            });
            cellStyleFormats.Count = UInt32Value.FromUInt32((uint)cellStyleFormats.ChildElements.Count);
            #endregion

            #region Cell format
            var cellFormats = new CellFormats();
            cellFormats.Append(
                new CellFormat(),
                new CellFormat
                {
                    NumberFormatId = 14,
                    FontId = 0,
                    FillId = 0,
                    BorderId = 0,
                    FormatId = 0,
                    ApplyNumberFormat = BooleanValue.FromBoolean(true)
                },
                new CellFormat
                {
                    NumberFormatId = 4,
                    FontId = 0,
                    FillId = 0,
                    BorderId = 0,
                    FormatId = 0,
                    ApplyNumberFormat = BooleanValue.FromBoolean(true)
                },
                new CellFormat
                {
                    NumberFormatId = DATETIME_FORMAT,
                    FontId = 0,
                    FillId = 0,
                    BorderId = 0,
                    FormatId = 0,
                    ApplyNumberFormat = BooleanValue.FromBoolean(true)
                },
                new CellFormat
                {
                    NumberFormatId = 3,
                    FontId = 0,
                    FillId = 0,
                    BorderId = 0,
                    FormatId = 0,
                    ApplyNumberFormat = BooleanValue.FromBoolean(true)
                },
                new CellFormat
                {
                    NumberFormatId = 4,
                    FontId = 0,
                    FillId = 0,
                    BorderId = 0,
                    FormatId = 0,
                    ApplyNumberFormat = BooleanValue.FromBoolean(true)
                },
                new CellFormat
                {
                    NumberFormatId = 10,
                    FontId = 0,
                    FillId = 0,
                    BorderId = 0,
                    FormatId = 0,
                    ApplyNumberFormat = BooleanValue.FromBoolean(true)
                },
                new CellFormat
                {
                    NumberFormatId = 4,
                    FontId = 0,
                    FillId = 0,
                    BorderId = 0,
                    FormatId = 0,
                    ApplyNumberFormat = BooleanValue.FromBoolean(true)
                },
                new CellFormat
                {
                    NumberFormatId = 4,
                    FontId = 1,
                    FillId = 0,
                    BorderId = 0,
                    FormatId = 0,
                    ApplyNumberFormat = BooleanValue.FromBoolean(true),
                    Alignment = new Alignment() { Horizontal = HorizontalAlignmentValues.Left }
                }
             );

            cellFormats.Append();
            cellFormats.Append();
            cellFormats.Append();
            cellFormats.Count = UInt32Value.FromUInt32((uint)cellFormats.ChildElements.Count);
            #endregion

            stylesheet.Append(
                numberingFormats,
                fonts,
                fills,
                borders,
                cellStyleFormats,
                cellFormats
             );

            var css = new CellStyles();
            css.Append(new CellStyle
            {
                Name = StringValue.FromString("Normal"),
                FormatId = 0,
                BuiltinId = 0
            });
            css.Count = UInt32Value.FromUInt32((uint)css.ChildElements.Count);
            stylesheet.Append(
                css,
                new DifferentialFormats { Count = 0 },
                new TableStyles
                {
                    Count = 0,
                    DefaultTableStyle = StringValue.FromString("TableStyleMedium9"),
                    DefaultPivotStyle = StringValue.FromString("PivotStyleLight16")
                }
            );



            return stylesheet;
        }
        private static ForegroundColor TranslateForeground(System.Drawing.Color fillColor)
        {
            return new ForegroundColor()
            {
                Rgb = new HexBinaryValue()
                {
                    Value =
                              System.Drawing.ColorTranslator.ToHtml(
                              System.Drawing.Color.FromArgb(
                                  fillColor.A,
                                  fillColor.R,
                                  fillColor.G,
                                  fillColor.B)).Replace("#", "")
                }
            };
        }

        #endregion
    }

}
