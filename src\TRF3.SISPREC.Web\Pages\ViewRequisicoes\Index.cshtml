@page
@using TRF3.SISPREC.Web.Menus
@using TRF3.SISPREC.Web.Pages.ViewRequisicoes.ViewRequisicao
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using Microsoft.AspNetCore.Authorization
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Requisições";
    PageLayout.Content.MenuItemName = SISPRECMenus.ViewRequisicao;
}

@section scripts
{
    <abp-script src="/Pages/ViewRequisicoes/index.js" />
    <abp-script src="/js/componente-utils.js" />
}
@section styles
{
    <abp-style src="/Pages/ViewRequisicoes/index.css" />
}

<abp-card>    
    <abp-card-body>
        <form asp-for="ViewRequisicaoFilter" id="ViewRequisicaoFilter">
            <abp-row class="d-flex justify-content-between align-items-center">
                <abp-column>
                    <abp-select asp-for="ViewRequisicaoFilter.CodTipoProced" />
                </abp-column>
                <abp-column>
                    <abp-input asp-for="ViewRequisicaoFilter.AnoPropos" />
                </abp-column>
                <abp-column>
                    <abp-select asp-for="ViewRequisicaoFilter.MesPropos" />
                </abp-column>
                <abp-column>
                    <input-requisicao-pesquisa asp-for="ViewRequisicaoFilter.NumProtocRequis" />
                </abp-column>

                <abp-column>
                    <abp-button class="float-end" button-type="Primary" text="Pesquisar" size="Small" id="btnPesquisar"></abp-button>
                </abp-column>

            </abp-row>
        </form>
        <hr/>
        <abp-table striped-rows="true" id="ViewRequisicaoTable" class="nowrap" />
    </abp-card-body>
</abp-card>