@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.Bancos.Banco
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Banco";
    PageLayout.Content.MenuItemName = SISPRECMenus.Banco;
}

@section scripts
{
    <abp-script src="/Pages/Bancos/index.js" />
}
@section styles
{
    <abp-style src="/Pages/Bancos/index.css"/>
}

<abp-card>
    <abp-card-header>
        <abp-row class="justify-content-between align-items-center">
            <abp-column>
                <a abp-collapse-id="BancoCollapse" class="text-secondary">Filtrar</a>
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="NewBancoButton"
                            text="Novo"
                            icon="plus"
                            button-type="Primary" />
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <abp-dynamic-form abp-model="BancoFilter" id="BancoFilter" required-symbols="false" column-size="_3">
            <abp-collapse-body id="BancoCollapse">
                <abp-form-content />
            </abp-collapse-body>
        </abp-dynamic-form>
        <abp-table striped-rows="true" id="BancoTable" class="nowrap"/>
    </abp-card-body>
</abp-card>
