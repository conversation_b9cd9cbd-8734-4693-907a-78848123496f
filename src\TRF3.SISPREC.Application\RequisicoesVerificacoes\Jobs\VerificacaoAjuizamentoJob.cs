using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.VerificacaoAjuizamentos;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs
{
    [ExcludeFromCodeCoverage]
    public class VerificacaoAjuizamentoJob : AsyncBackgroundJob<VerificacaoAjuizamentoArgs>, IVerificacaoAjuizamentoJob
    {
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly IVerificacaoAjuizamentoManager _verificacaoAjuizamentoManager;

        public VerificacaoAjuizamentoJob(IUnitOfWorkManager unitOfWorkManager,
                                         IVerificacaoAjuizamentoManager verificacaoAjuizamentoManager)
        {
            _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
            _verificacaoAjuizamentoManager = verificacaoAjuizamentoManager ?? throw new ArgumentNullException(nameof(verificacaoAjuizamentoManager));
        }

        public override async Task ExecuteAsync(VerificacaoAjuizamentoArgs args)
        {
            try
            {
                using (var uow = _unitOfWorkManager.Begin(false, true))
                {
                    await _verificacaoAjuizamentoManager.ProcessarVerificacaoAsync(args.NumeroProtocoloRequisicao);
                    await uow.CompleteAsync();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Erro ao executar VerificacaoAjuizamentoJob para requisição nº {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
                throw;
            }
        }
    }

    [ExcludeFromCodeCoverage]
    public class VerificacaoAjuizamentoArgs : BaseBackgroundJobArgs
    {
        public string NumeroProtocoloRequisicao { get; set; }

        public VerificacaoAjuizamentoArgs(string numeroProtocoloRequisicao)
        {
            if (string.IsNullOrEmpty(numeroProtocoloRequisicao) || string.IsNullOrWhiteSpace(numeroProtocoloRequisicao))
                throw new ArgumentException($"Erro ao executar verificação de ajuizamento. Nº requisição inválido: {numeroProtocoloRequisicao}.");

            NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
        }
    }
}
