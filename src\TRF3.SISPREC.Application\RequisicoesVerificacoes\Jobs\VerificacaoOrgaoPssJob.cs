using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using TRF3.SISPREC.VerificacaoOrgaoPSS;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoOrgaoPssJob : AsyncBackgroundJob<VerificacaoOrgaoPssArgs>, IVerificacaoOrgaoPssJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IVerificacaoOrgaoPssManager _verificacaoOrgaoPssManager;

    public VerificacaoOrgaoPssJob(IUnitOfWorkManager unitOfWorkManager, IVerificacaoOrgaoPssManager verificacaoOrgaoPssManager)
    {
        _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
        _verificacaoOrgaoPssManager = verificacaoOrgaoPssManager ?? throw new ArgumentNullException(nameof(verificacaoOrgaoPssManager));
    }

    public override async Task ExecuteAsync(VerificacaoOrgaoPssArgs args)
    {
        try
        {
            using (var uow = _unitOfWorkManager.Begin(false, true))
            {
                await _verificacaoOrgaoPssManager.ProcessarVerificacaoAsync(args.NumeroProtocoloRequisicao);
                await uow.CompleteAsync();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoOrgaoPssJob para requisição nº {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
            throw;
        }
    }
}

[ExcludeFromCodeCoverage]
public class VerificacaoOrgaoPssArgs : BaseBackgroundJobArgs
{
    public string NumeroProtocoloRequisicao { get; set; }

    public override string JobGroupName => VerificacaoRequisicoesSettings.VerificacaoOrgaoPssJobGroupName;

    public VerificacaoOrgaoPssArgs(string numeroProtocoloRequisicao)
    {
        if (string.IsNullOrEmpty(numeroProtocoloRequisicao) || string.IsNullOrWhiteSpace(numeroProtocoloRequisicao))
            throw new ArgumentException($"Erro ao executar verificação de órgão PSS. Nº requisição inválido: {numeroProtocoloRequisicao}.");

        NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
    }
}
