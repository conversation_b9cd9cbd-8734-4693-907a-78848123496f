$(function () {

    $("#btnPesquisar").click(function (event) {
        event.preventDefault();
        dataTable.ajax.reload();
    });

    const getFilter = function () {
        const input = {};
        $("#ExpedienteAdministrativoFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/ExpedienteAdministrativoFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.expedientesAdministrativo.expedienteAdministrativo;
    const createModal = new abp.ModalManager(
        {
            viewUrl: abp.appPath + 'ExpedientesAdministrativos/CreateModal'
        });

    const dataTable = $('#ExpedienteAdministrativoTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        ordering : false,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: false,
        scrollCollapse: true,
        ajax: abp.libs.datatables.createAjax(service.getExpedientesSemBloco, getFilter),
        columnDefs: [
            {
                data: null,
                render: function (data, type, row) {
                    return `<input type="checkbox" class="row-checkbox" data-id="${row.expedienteAdministrativoId}" data-tipo="${row.tipoExpedienteAdministrativo}"/>`;
                }
            },
            {
                title: "Nº Expediente",
                data: "numeroProcessoSei"
            },
            {
                title: "Tipo",
                data: "tipoExpedienteAdministrativo"
            },
            {
                title: "Data",
                data: "dataExpedienteAdministrativo",
                render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss')
            },
            {
                title: "Usuário",
                data: "nomeUsuario"
            },
            {
                title: "Motivo",
                data: "observacaoExpedienteAdministrativo"
            }
        ]
    }));


    aplicarCheckedEmTable(document, '#ExpedienteAdministrativoTable');

    const selecionarBlocos = function () {
        const itensSelecionados = [];

        $('.row-checkbox:checked').each(function () {
            const id = $(this).data('id');
            const tipo = $(this).data('tipo');
            itensSelecionados.push({ Id: id, Tipo: tipo });
        });

        const tiposUnicos = new Set(
            itensSelecionados.map(item => item.Tipo.toLowerCase())
        );

        if (tiposUnicos.size > 1) {
            abp.notify.error('Para incluir em um bloco, todos os expedientes devem ser do mesmo Tipo.');
            return null
        }
        return itensSelecionados
    }

    $('#gerar-bloco').on('click', function () {
        let itensSelecionados = selecionarBlocos()

        if (!itensSelecionados || itensSelecionados.length === 0) {
            abp.notify.warn("Necessário selecionar ao menos um expediente.")
            return;
        }

        let mensagem = 'Tem certeza de que deseja gerar novo bloco?'
        alertaBotoesInvertidos(mensagem, '', 'warning')
            .then((result) => {
                if (result.isConfirmed) {
                    // Se o usuário confirmar, executa a ação
                    if (result.isConfirmed) {
                        abp.ui.block({ elm: 'body', busy: true });

                        service.gerarBloco(itensSelecionados)
                            .then(function () {
                                abp.notify.success('Expedientes incluídos no novo bloco com sucesso!');
                                dataTable.ajax.reload();
                            })
                            .always(function () {
                                abp.ui.unblock();
                            });
                    }
                }
            });
    });

    $('#incluir-bloco').on('click', function () {
        let itensSelecionados = selecionarBlocos()

        if (!itensSelecionados || itensSelecionados.length === 0) {
            abp.notify.warn("Necessário selecionar ao menos um expediente.")
            return;
        }
        createModal.open(
            {
                Tipo: itensSelecionados[0].Tipo,
                Id: itensSelecionados.map(item => item.Id).join(',')
            }
        );
    })

    createModal.onResult(function () {
        abp.notify.success("Expedientes incluídos no bloco com sucesso!")
        dataTable.ajax.reload();
    });

});
