using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using TRF3.SISPREC.VerificacaoSuplementarIgual;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoSuplementarIgualJob : AsyncBackgroundJob<VerificacaoSuplementarIgualJobArgs>, IVerificacaoSuplementarIgualJob
{
    private readonly IVerificacaoSuplementarIgualService _verificacaoSuplementarIgualService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public VerificacaoSuplementarIgualJob(IVerificacaoSuplementarIgualService verificacaoSuplementarIgualService, IUnitOfWorkManager unitOfWorkManager)
    {
        _verificacaoSuplementarIgualService = verificacaoSuplementarIgualService;
        _unitOfWorkManager = unitOfWorkManager;
    }

    [ExcludeFromCodeCoverage]
    public override async Task ExecuteAsync(VerificacaoSuplementarIgualJobArgs args)
    {
        try
        {
            if (args.RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar VerificacaoSuplementarIgualJob. RequisicaoVerificacaoId inválido: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
                return;
            }

            if (string.IsNullOrEmpty(args.NumeroProtocoloRequisicao))
            {
                Logger.LogError("Erro ao executar VerificacaoSuplementarIgualJob. NumeroProtocoloRequisicao inválido: NumeroProtocoloRequisicao: {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
                return;
            }

            using IUnitOfWork uow = _unitOfWorkManager.Begin(false, true);
            await _verificacaoSuplementarIgualService.VerificarSuplementarAsync(args.RequisicaoVerificacaoId, args.NumeroProtocoloRequisicao);
            await uow.CompleteAsync();

        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoSuplementarIgualJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
            throw;
        }
    }
}

[ExcludeFromCodeCoverage]
public class VerificacaoSuplementarIgualJobArgs : BaseBackgroundJobArgs
{
    public long RequisicaoVerificacaoId { get; set; }
    public string NumeroProtocoloRequisicao { get; set; }

    public override string JobGroupName => VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualJobGroupName;

    public VerificacaoSuplementarIgualJobArgs(long requisicaoVerificacaoId, string numeroProtocoloRequisicao)
    {
        RequisicaoVerificacaoId = requisicaoVerificacaoId;
        NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
    }
}