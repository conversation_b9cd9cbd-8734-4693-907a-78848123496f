using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using MockQueryable.NSubstitute;
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.IndicadorEconomicoTipos;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.SincronizacaoLegado;
using TRF3.SISPREC.SincronizacaoLegado.Models.ReqPag;
using TRF3.SISPREC.SincronizacaoLegado.Repositories.Interfaces;
using TRF3.SISPREC.SincronizacaoLegado.Repositories.ReqPag;
using TRF3.SISPREC.Unidades;
using TRF3.SISPREC.UnidadesOrcamentarias;
using Volo.Abp;
using IIndicadorEconomicoSISPRECRepository = TRF3.SISPREC.IndicadorEconomicos.IIndicadorEconomicoRepository;
using IndicadorEconomico = TRF3.SISPREC.IndicadorEconomicos.IndicadorEconomico;
using Proposta = TRF3.SISPREC.Propostas.Proposta;
using PropostaReqPag = TRF3.SISPREC.SincronizacaoLegado.Models.ReqPag.Proposta;

namespace TRF3.SISPREC.Processos
{
    public class ImportarPropostaServiceTests : SISPRECDomainTestBase<SISPRECDomainTestModule>
    {
        private IReqPagUnitOfWork _reqPagUnitOfWork;
        private IPropostaRepository _propostaSISPRECRepository;
        private IUnidadeOrcamentariaRepository _unidadeRepository;
        private IIndicadorEconomicoSISPRECRepository _indicadorEconomicoSISPRECRepository;
        private IPropostaReqPagRepository _propostaReqPagRepository;
        private ICadastroPessoaUnidadeRepository _cadastroPessoaUnidadeRepository;
        private IImportarPropostaService _importarPropostaService;
        private IUnidadeOrcamentariaManager _unidadeOrcamentariaManager;
        private IUnidadePessoaService _unidadePessoaService;
        private PropostaReqPag propostaReqPag;
        public ImportarPropostaServiceTests()
        {
            _importarPropostaService = GetRequiredService<IImportarPropostaService>();
        }

        protected override void AfterAddApplication(IServiceCollection services)
        {
            base.AfterAddApplication(services);

            // Create mocks
            _reqPagUnitOfWork = Substitute.For<IReqPagUnitOfWork>();
            _propostaSISPRECRepository = Substitute.For<IPropostaRepository>();
            _unidadeRepository = Substitute.For<IUnidadeOrcamentariaRepository>();
            _indicadorEconomicoSISPRECRepository = Substitute.For<IIndicadorEconomicoSISPRECRepository>();
            _cadastroPessoaUnidadeRepository = Substitute.For<ICadastroPessoaUnidadeRepository>();
            _unidadeOrcamentariaManager = Substitute.For<IUnidadeOrcamentariaManager>();
            _unidadePessoaService = Substitute.For<IUnidadePessoaService>();
            _propostaReqPagRepository = Substitute.For<IPropostaReqPagRepository>();
            _reqPagUnitOfWork.PropostaReqPagRepository.Returns(_propostaReqPagRepository);
            _reqPagUnitOfWork.CadastroPessoaUnidadeRepository.Returns(_cadastroPessoaUnidadeRepository);

            // Setup the mocks
            _reqPagUnitOfWork.PropostaReqPagRepository.Returns(_propostaReqPagRepository);
            _reqPagUnitOfWork.CadastroPessoaUnidadeRepository.Returns(_cadastroPessoaUnidadeRepository);
            propostaReqPag = new PropostaReqPag
            {
                cod_tipo_proced = "PRC",
                cod_cadast_entida = 1,
                ano_propos = 2024,
                num_propos = "001",
                dat_atuali = DateTime.Now,
                val_minimo = 1000,
                val_maximo = 5000,
                val_minimo_parcel = 100,
                qtd_maxima_parcel_alimet = "5",
                qtd_maxima_parcel_comum = "10",
                qtd_maxima_parcel_desapr_unico = "3",
                qtd_maxima_parcel_desapr = "4",
                dat_indica_econom = DateTime.Now.AddMonths(-1),
                cod_tipo_indica_econom = "INF",
                cod_situac_propos = Convert.ToChar(ESituacaoProposta.PENDENTE).ToString(),
                dat_inicio_calcul_juros = DateTime.Now
            };

            var propostasReqPag = new List<PropostaReqPag> { propostaReqPag };
            _propostaReqPagRepository.BuscaPropostasRPVDoisUltimosMesesEProximo().Returns(new List<PropostaReqPag>());
            _propostaReqPagRepository.BuscaPropostasPCTAnoMaiorOuIgualAoAtual().Returns(propostasReqPag);

            var indicadores = new List<IndicadorEconomico>
            {
                new IndicadorEconomico
                {
                    IndicadorEconomicoId = 1,
                    DataIndicador = propostaReqPag.dat_indica_econom.Value,
                    IndicadorEconomicoTipoId = 1,
                    TipoIndicadorEconomico = new IndicadorEconomicoTipo(1, propostaReqPag.cod_tipo_indica_econom, "Apenas um teste")
                }
            }.AsQueryable();

            _indicadorEconomicoSISPRECRepository.GetQueryableAsync().Returns(Task.FromResult(indicadores));

            _unidadeRepository.ObterUnidade(propostaReqPag.cod_cadast_entida).Returns(1);
            var cadastroPessoaUnidadOrcame = new List<CadastroPessoaUnidadOrcame> {new CadastroPessoaUnidadOrcame
            {
                cod_cadast_pessoa = 1,
                cod_siafi = 1,
                cod_orgao_unidad_orcame_siafi = 2024,
                nom_pessoa = "ASD",
                cod_tipo_pessoa = "O",
                num_cnpj_cpf = "1000",
                ide_advoga_procur = "S",
                ide_situac_pessoa = "S",

            }}.AsEnumerable();

            _cadastroPessoaUnidadeRepository.BuscaPorUnidadePorCodigo(Arg.Any<int>()).Returns(Task.FromResult(cadastroPessoaUnidadOrcame));
            _cadastroPessoaUnidadeRepository.BuscaPorCodigo(Arg.Any<int>()).Returns(Task.FromResult(cadastroPessoaUnidadOrcame));


            // Remove existing registrations if any
            services.RemoveAll<IReqPagUnitOfWork>();
            services.RemoveAll<IPropostaRepository>();
            services.RemoveAll<IUnidadeOrcamentariaRepository>();
            services.RemoveAll<IIndicadorEconomicoSISPRECRepository>();
            services.RemoveAll<ICadastroPessoaUnidadeRepository>();
            services.RemoveAll<IPropostaReqPagRepository>();
            services.RemoveAll<IUnidadeOrcamentariaManager>();
            services.RemoveAll<IUnidadePessoaService>();
            // Register mocks
            services.AddTransient(typeof(IReqPagUnitOfWork), _ => _reqPagUnitOfWork);
            services.AddTransient(typeof(IPropostaRepository), _ => _propostaSISPRECRepository);
            services.AddTransient(typeof(IUnidadeOrcamentariaRepository), _ => _unidadeRepository);
            services.AddTransient(typeof(IIndicadorEconomicoSISPRECRepository), _ => _indicadorEconomicoSISPRECRepository);
            services.AddTransient(typeof(ICadastroPessoaUnidadeRepository), _ => _cadastroPessoaUnidadeRepository);
            services.AddTransient(typeof(IPropostaReqPagRepository), _ => _propostaReqPagRepository);
            services.AddTransient(typeof(IUnidadeOrcamentariaManager), _ => _unidadeOrcamentariaManager);
            services.AddTransient(typeof(IUnidadePessoaService), _ => _unidadePessoaService);
            // Register ImportarPropostaService
            services.AddTransient<IImportarPropostaService, ImportarPropostaService>();
        }


        [Fact]
        public async Task Importar_Deve_Passar()
        {
            // Arrange
            _propostaReqPagRepository.BuscaPropostasRPVDoisUltimosMesesEProximo().Returns(new List<PropostaReqPag>());
            _propostaReqPagRepository.BuscaPropostasPCTAnoMaiorOuIgualAoAtual().Returns(new List<PropostaReqPag>());

            // Act
            var exception = await Record.ExceptionAsync(async () => await _importarPropostaService.Importar());

            // Assert
            Assert.Null(exception);
        }

        [Fact]
        public async Task Importar_Deve_Atualizar_PropostaExistente()
        {
            // Arrange 

            _propostaSISPRECRepository.ExisteProposta(Arg.Any<Proposta>()).Returns(Task.FromResult(100));
            _unidadeOrcamentariaManager.BuscaUnidadeOrcamentaria(Arg.Any<int>(), Arg.Any<string>()).Returns(Task.FromResult(1));
            // Act
            var exception = await Record.ExceptionAsync(_importarPropostaService.Importar);

            // Assert
            Assert.Null(exception);
            await _propostaSISPRECRepository.Received(1).ExisteProposta(Arg.Any<Proposta>());
            await _propostaSISPRECRepository.Received(1).UpdateAsync(Arg.Is<Proposta>(p => p.PropostaId == 100), true);
            await _propostaSISPRECRepository.DidNotReceive().InsertAsync(Arg.Any<Proposta>(), true);
        }

        [Fact]
        public async Task Importar_Deve_Inserir_Nova_Proposta()
        {
            // Act
            _unidadeOrcamentariaManager.BuscaUnidadeOrcamentaria(Arg.Any<int>(), Arg.Any<string>()).Returns(Task.FromResult(1));
            var exception = await Record.ExceptionAsync(_importarPropostaService.Importar);

            // Assert
            Assert.Null(exception);
            await _propostaSISPRECRepository.Received(1).ExisteProposta(Arg.Any<Proposta>());
            await _propostaSISPRECRepository.Received(1).InsertAsync(Arg.Any<Proposta>(), true);
            await _propostaSISPRECRepository.DidNotReceive().UpdateAsync(Arg.Any<Proposta>(), true);
        }

        [Fact]
        public async Task Importar_Deve_Inserir_Nova_Proposta_UnidadeTipoM()
        {
            // Act
            var cadastroPessoaUnidadOrcame = new List<CadastroPessoaUnidadOrcame> {new CadastroPessoaUnidadOrcame
            {
                cod_cadast_pessoa = 1,
                cod_siafi = 1,
                cod_orgao_unidad_orcame_siafi = 2024,
                nom_pessoa = "ASD",
                cod_tipo_pessoa = "M",
                num_cnpj_cpf = "1000",
                ide_advoga_procur = "S",
                ide_situac_pessoa = "S",

            }}.AsEnumerable();
            _cadastroPessoaUnidadeRepository.BuscaPorUnidadePorCodigo(Arg.Any<int>()).Returns(Task.FromResult(cadastroPessoaUnidadOrcame));

            _unidadePessoaService.BuscarEInserirSeNaoEncontrar(Arg.Any<string>(), Arg.Any<string>(), ETipoPessoa.M).Returns(Task.FromResult(1));
            var exception = await Record.ExceptionAsync(_importarPropostaService.Importar);

            // Assert
            Assert.Null(exception);
            await _propostaSISPRECRepository.Received(1).ExisteProposta(Arg.Any<Proposta>());
            await _propostaSISPRECRepository.Received(1).InsertAsync(Arg.Any<Proposta>(), true);
            await _propostaSISPRECRepository.DidNotReceive().UpdateAsync(Arg.Any<Proposta>(), true);
        }

        [Fact]
        public async Task ObterUnidadeId_Deve_Retornar_Exception_Unidade()
        {
            // Act
            _cadastroPessoaUnidadeRepository.BuscaPorUnidadePorCodigo(Arg.Any<int>())
            .Returns(Task.FromResult(Enumerable.Empty<CadastroPessoaUnidadOrcame>()));

            // Assert
            var message = await Should.ThrowAsync<UserFriendlyException>(() => _importarPropostaService.Importar());
            message.Message.ShouldBe($"Não foi possível importar a proposta {propostaReqPag.ToString()}. Código Siafi não encontrado no ReqPag.");
        }


        [Fact]
        public async Task ObterUnidadeId_Deve_Retornar_Exception_CodigoSiafi()
        {
            // Act
            var unidade = new CadastroPessoaUnidadOrcame { cod_siafi = 1 };
            _unidadeOrcamentariaManager.BuscaUnidadeOrcamentaria(Arg.Any<int>(), Arg.Any<string>()).Returns(Task.FromResult(0));

            // Assert
            var message = await Should.ThrowAsync<UserFriendlyException>(() => _importarPropostaService.Importar());
            message.Message.ShouldBe($"Não foi possível importar a proposta {propostaReqPag.ToString()}. Unidade não encontrada no SISPREC com Código Siafi: {unidade.cod_siafi}.");
        }


        [Fact]
        public async Task ObterIndicadorEconomicoId_Deve_Retornar_Exception_IndicadorEconomicoId()
        {
            // Act
            _unidadeOrcamentariaManager.BuscaUnidadeOrcamentaria(Arg.Any<int>(), Arg.Any<string>()).Returns(Task.FromResult(1));
            var empty = new List<IndicadorEconomico>().AsQueryable().BuildMockDbSet();
            _indicadorEconomicoSISPRECRepository.GetQueryableAsync().Returns(empty);

            // Assert
            var message = await Should.ThrowAsync<UserFriendlyException>(() => _importarPropostaService.Importar());
            message.Message.ShouldBe($"Não foi possível importar a proposta {propostaReqPag.ToString()}. Indicador Econômico não encontrado no SISPREC. DataIndicador: {propostaReqPag.dat_indica_econom},  TipoIndicadorEconomico: {propostaReqPag.cod_tipo_indica_econom}");
        }


    }
}
