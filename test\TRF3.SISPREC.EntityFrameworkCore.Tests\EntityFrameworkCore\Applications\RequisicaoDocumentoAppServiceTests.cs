using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using MockQueryable.NSubstitute;
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.ArquivoServices;
using TRF3.SISPREC.EntityFrameworkCore;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicoesDocumentos.Dtos;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos;

namespace TRF3.SISPREC.RequisicoesDocumentos;

public class RequisicaoDocumentoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly IRequisicaoPropostaRepository _requisicaoPropostaRepository;
    private readonly IRequisicaoDocumentoAppService _appService;
    private readonly IRequisicaoDocumentoRepository _repository;
    private readonly IRequisicaoDocumentoService _service;
    private readonly RequisicaoDocumento requisicaoDocumentoObj1;
    private readonly RequisicaoDocumento requisicaoDocumentoObj2;
    private readonly RequisicaoProtocolo requisicaoProtocolo;

    public RequisicaoDocumentoAppServiceTests()
    {
        _appService = GetRequiredService<IRequisicaoDocumentoAppService>();
        _repository = GetRequiredService<IRequisicaoDocumentoRepository>();
        _service = GetRequiredService<IRequisicaoDocumentoService>();
        _requisicaoPropostaRepository = GetRequiredService<IRequisicaoPropostaRepository>();

        requisicaoProtocolo = RequisicaoTesteHelper.CriarRequisicaoProtocolo(ServiceProvider);

        var fakers = new Bogus.Faker<RequisicaoDocumento>()
            .RuleFor(p => p.RequisicaoDocumentoId, p => p.Random.Long(min: 1, max: long.MaxValue))
            .RuleFor(p => p.NomeDocumento, p => p.Random.String())
            .RuleFor(p => p.Path, p => p.Random.String())
            .RuleFor(p => p.DataCriacao, p => DateTime.Now)
            .RuleFor(p => p.NumeroProtocoloRequisicao, requisicaoProtocolo.NumeroProtocoloRequisicao)
            .RuleFor(p => p.IsDeleted, p => false)
            .Generate(2);

        requisicaoDocumentoObj1 = fakers[0];
        requisicaoDocumentoObj2 = fakers[1];

        _repository.InsertAsync(requisicaoDocumentoObj1, true).Wait();
        _repository.InsertAsync(requisicaoDocumentoObj2, true).Wait();
    }

    protected override void AfterAddApplication(IServiceCollection services)
    {
        base.AfterAddApplication(services);
        services.RemoveAll<IRequisicaoDocumentoService>();
        services.RemoveAll<IRequisicaoPropostaRepository>();
        services.RemoveAll<IArquivoService>();


        var service = Substitute.For<IRequisicaoDocumentoService>();
        var repositoy = Substitute.For<IRequisicaoPropostaRepository>();
        var arquivoService = Substitute.For<IArquivoService>();

        services.AddSingleton(service);
        services.AddSingleton(repositoy);
        services.AddSingleton(arquivoService);
    }

    [Fact]
    public async Task GerarEspelhoRequisicao_Deve_Gerar_Espelho()
    {
        //Arrange

        var dto = new CreateEspelhoRequisicaoDto()
        {
            NumeroRequisicaoPrincipal = new List<string>() {
                requisicaoProtocolo.NumeroProtocoloRequisicao,
            },
            Observacao = "Apenas um teste",
            Procedimento = "RPV",
            RequisicoesComparada = new List<RequisicaoJustificativas.Dtos.RequisicaoComparadaDto>()
        };

        var propostasMock = new List<RequisicaoProposta>
        {
            new RequisicaoProposta
            {
                NumeroProtocoloRequisicao = requisicaoProtocolo.NumeroProtocoloRequisicao,
                Proposta = new Proposta { AnoProposta = 2023, MesProposta = 8 }
            }
        }.AsQueryable();
        var asyncPropostasMock = propostasMock.AsQueryable().BuildMock();
        _requisicaoPropostaRepository.GetQueryableAsync().Returns(Task.FromResult(asyncPropostasMock));

        _service
            .GerarEspelhoRequisicaoAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>())
            .Returns(Task.CompletedTask);

        //Act
        await _appService.GerarEspelhoRequisicaoAsync(dto);

        //Assert
        await _service.Received(1)
            .GerarEspelhoRequisicaoAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>());
    }

    [Fact]
    public async Task ExcluirEspelhoRequisicao_Deve_Excluir_Espelho()
    {
        //Arrange

        var dto = new CreateEspelhoRequisicaoDto()
        {
            NumeroRequisicaoPrincipal = new List<string>() {
                requisicaoProtocolo.NumeroProtocoloRequisicao,
            },
            Observacao = "Apenas um teste",
            Procedimento = "RPV",
            RequisicoesComparada = new List<RequisicaoJustificativas.Dtos.RequisicaoComparadaDto>()
        };

        var propostasMock = new List<RequisicaoProposta>
        {
            new RequisicaoProposta
            {
                NumeroProtocoloRequisicao = requisicaoProtocolo.NumeroProtocoloRequisicao,
                Proposta = new Proposta { AnoProposta = 2023, MesProposta = 8 }
            }
        }.AsQueryable();
        var asyncPropostasMock = propostasMock.AsQueryable().BuildMock();
        _requisicaoPropostaRepository.GetQueryableAsync().Returns(Task.FromResult(asyncPropostasMock));

        _service
            .GerarEspelhoRequisicaoAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>())
            .Returns(Task.CompletedTask);

        //Act
        await _appService.GerarEspelhoRequisicaoAsync(dto);

        await _appService.ExcluirEspelhoRequisicaoAsync(dto.NumeroRequisicaoPrincipal[0]);

        //Assert
        await _service.Received(1).ExcluirEspelhoRequisicaoAsync(Arg.Any<string>());
    }

    [Fact]
    public async Task Criar_RequisicaoDocumento_Deve_Passar()
    {
        // Arrange
        var input = new Bogus.Faker<CreateUpdateRequisicaoDocumentoDto>()
            .RuleFor(p => p.NomeDocumento, p => p.Random.Hash())
            .RuleFor(p => p.Path, p => p.Random.Hash())
            .RuleFor(p => p.DataCriacao, p => DateTime.Now)
            .RuleFor(p => p.NumeroProtocoloRequisicao, requisicaoProtocolo.NumeroProtocoloRequisicao)
            .RuleFor(p => p.IsDeleted, p => false)
            .Generate();

        // Act
        var result = await _appService.CreateAsync(input);

        // Assert
        result.ShouldNotBeNull();
        result.RequisicaoDocumentoId.ShouldBeGreaterThan(0);
        result.NomeDocumento.ShouldBe(input.NomeDocumento);
        result.Path.ShouldBe(input.Path);
        result.DataCriacao.ShouldBe(input.DataCriacao);
        result.NumeroProtocoloRequisicao.ShouldBe(input.NumeroProtocoloRequisicao);
        result.IsDeleted.ShouldBe(input.IsDeleted);
    }

    [Fact]
    public async Task Atualizar_RequisicaoDocumento_Deve_Passar()
    {
        // Arrange
        var objetoOriginal = _repository.GetListAsync().Result.FirstOrDefault();
        var input = new Bogus.Faker<CreateUpdateRequisicaoDocumentoDto>()
            .RuleFor(p => p.NomeDocumento, p => p.Random.String())
            .RuleFor(p => p.Path, p => p.Random.String())
            .RuleFor(p => p.DataCriacao, p => DateTime.Now)
            .RuleFor(p => p.NumeroProtocoloRequisicao, requisicaoProtocolo.NumeroProtocoloRequisicao)
            .RuleFor(p => p.IsDeleted, p => p.Random.Bool())
            .Generate();

        // Act
        var result = await _appService.UpdateAsync(objetoOriginal.RequisicaoDocumentoId, input);

        // Assert
        result.ShouldNotBeNull();
        result.RequisicaoDocumentoId.ShouldBeGreaterThan(0);
        result.NomeDocumento.ShouldBe(input.NomeDocumento);
        result.Path.ShouldBe(input.Path);
        result.DataCriacao.ShouldBe(input.DataCriacao);
        result.NumeroProtocoloRequisicao.ShouldBe(input.NumeroProtocoloRequisicao);
        result.IsDeleted.ShouldBe(input.IsDeleted);
    }

    [Fact]
    public async Task Excluir_RequisicaoDocumento_Deve_Passar()
    {
        // Arrange
        var objetoParaExcluir = new Bogus.Faker<RequisicaoDocumento>()
            .RuleFor(p => p.RequisicaoDocumentoId, p => p.Random.Long(min: 1))
            .RuleFor(p => p.NomeDocumento, p => p.Random.String())
            .RuleFor(p => p.Path, p => p.Random.String())
            .RuleFor(p => p.DataCriacao, p => DateTime.Now)
            .RuleFor(p => p.NumeroProtocoloRequisicao, requisicaoProtocolo.NumeroProtocoloRequisicao)
            .RuleFor(p => p.IsDeleted, p => p.Random.Bool())
            .Generate();

        objetoParaExcluir = await _repository.InsertAsync(objetoParaExcluir, autoSave: true);

        // Act
        await _appService.DeleteAsync(objetoParaExcluir.RequisicaoDocumentoId);

        // Assert
        var objetoDeletado = await _repository.FindAsync(a => a.RequisicaoDocumentoId == objetoParaExcluir.RequisicaoDocumentoId);
        objetoDeletado.ShouldBeNull();
    }

    [Fact]
    public async Task Obter_RequisicaoDocumento_Por_Id_Deve_Passar()
    {
        // Arrange
        var objetoExistente = await _repository.FindAsync(a => a.RequisicaoDocumentoId == requisicaoDocumentoObj1.RequisicaoDocumentoId);

        // Act
        var result = await _appService.GetAsync(objetoExistente.RequisicaoDocumentoId);

        // Assert
        result.ShouldNotBeNull();
        result.RequisicaoDocumentoId.ShouldBe(objetoExistente.RequisicaoDocumentoId);
    }

    [Fact]
    public async Task Obter_RequisicaoDocumento_Com_Ordenacao_Padrao_Deve_Passar()
    {
        // Arrange
        var input = new RequisicaoDocumentoGetListInput();  // Sem filtros específicos para testar a ordenação padrão.

        // Act
        var result = await _appService.GetListAsync(input);

        // Assert
        result.Items.ShouldNotBeEmpty(); // Correção aplicada diretamente ao acessar a propriedade Items
        result.Items.OrderBy(l => l.RequisicaoDocumentoId).SequenceEqual(result.Items).ShouldBeTrue(); // Corrigido para usar OrderBy e SequenceEqual para verificar a ordem
    }
}

