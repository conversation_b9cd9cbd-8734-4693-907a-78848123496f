

using Bogus;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Shouldly;
using System.Linq.Expressions;
using TRF3.SISPREC.ControleImportacaoRequisicaoErros;
using TRF3.SISPREC.ControleImportacaoRequisicoes;
using TRF3.SISPREC.ControleImportacoes;
using TRF3.SISPREC.ControleImportacoes.Dtos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Pessoas.SinPessoasReqPag;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.SincronizacaoLegado;
using TRF3.SISPREC.SincronizacaoLegado.Models.ReqPag;
using TRF3.SISPREC.Unidades;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.ObjectMapping;
using Volo.Abp.SettingManagement;
using Proposta = TRF3.SISPREC.Propostas.Proposta;

namespace TRF3.SISPREC.EntityFrameworkCore.Applications
{
    public class ControleImportacaoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
    {
        private ISinPessoaReqPagManager _sinPessoaReqPagManager;
        private ISinPessoaReqPagRepository _sinPessoaReqPagRepositoy;
        private ControleImportacaoAppService _sinPessoaAppService;
        private IControleImportacaoRequisicaoRepository _controleRepository;
        private ISettingManager _settingManager;
        private IPropostaRepository _propostaRepository;
        private IReqPagUnitOfWork _reqPagUnitOfWork;
        private SinPessoaReqPag sinPessoaReqObj1;
        private readonly IObjectMapper _objectMapper;
        public ControleImportacaoAppServiceTests()
        {
            _controleRepository = GetRequiredService<IControleImportacaoRequisicaoRepository>();
            CriaControle().GetAwaiter().GetResult();
            _sinPessoaAppService = GetRequiredService<ControleImportacaoAppService>();

            _objectMapper = Substitute.For<IObjectMapper>();
            CreateRandomSinPessoa().GetAwaiter().GetResult();
        }

        protected override void AfterAddApplication(IServiceCollection services)
        {
            AdicionarMocksServicosSinPessoa(services);

            base.AfterAddApplication(services);
        }

        private void AdicionarMocksServicosSinPessoa(IServiceCollection services)
        {

            services.RemoveAll(typeof(ISinPessoaReqPagManager));
            _sinPessoaReqPagManager = Substitute.For<ISinPessoaReqPagManager>();
            services.AddTransient(typeof(ISinPessoaReqPagManager), _ => _sinPessoaReqPagManager);

            services.RemoveAll(typeof(IControleImportacaoRequisicaoRepository));
            _controleRepository = Substitute.For<IControleImportacaoRequisicaoRepository>();
            services.AddTransient(typeof(IControleImportacaoRequisicaoRepository), _ => _controleRepository);

            services.RemoveAll(typeof(IReqPagUnitOfWork));
            _reqPagUnitOfWork = Substitute.For<IReqPagUnitOfWork>();
            services.AddTransient(typeof(IReqPagUnitOfWork), _ => _reqPagUnitOfWork);

            services.RemoveAll(typeof(IPropostaRepository));
            _propostaRepository = Substitute.For<IPropostaRepository>();
            services.AddTransient(typeof(IPropostaRepository), _ => _propostaRepository);

            var relRequisicaoPropostaRepository = CriaPropostaReqPag();

            _sinPessoaReqPagManager.BuscaPropostaPorRequisicao(Arg.Any<string>()).Returns(relRequisicaoPropostaRepository);
            _reqPagUnitOfWork.RelRequisicaoPropostaRepository.BuscaPorRequisicao(Arg.Any<string>()).Returns(relRequisicaoPropostaRepository);
            _sinPessoaReqPagManager = new SinPessoaReqPagManager(_sinPessoaReqPagRepositoy, _controleRepository, _reqPagUnitOfWork, _propostaRepository);
        }

        [Fact]
        public async Task ValidaProtocolo_Deve_ReturnsException_RequsicaoImportada()
        {
            //Arrange
            var controle = new ControleImportacaoRequisicao();
            controle.PropostaId = 1;
            controle.NumeroProtocoloRequisicao = "1234";
            controle.Status = Enums.EStatusImportacao.PENDENTE;
            _controleRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<ControleImportacaoRequisicao, bool>>>())
            .Returns(controle);

            //Act && Arrange 
            var message = await Assert.ThrowsAsync<UserFriendlyException>(() => _sinPessoaReqPagManager.ValidaProtocolo("1234"));
            message.Message.ShouldBe("Protocolo de Requisição já importada!");
        }

        [Fact]
        public async Task ValidaProtocolo_Deve_ReturnsException_PropostaNaoEncontrada()
        {
            // Arrange
            var proposta = await CreateRandomProposta();
            var controle = new ControleImportacaoRequisicao();
            controle.PropostaId = proposta.PropostaId;
            controle.NumeroProtocoloRequisicao = "1234";
            controle.Status = Enums.EStatusImportacao.PENDENTE;

            _controleRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<ControleImportacaoRequisicao, bool>>>()).Returns((ControleImportacaoRequisicao)null);

            //Act && Arrange 
            var message = await Assert.ThrowsAsync<UserFriendlyException>(() => _sinPessoaReqPagManager.ValidaProtocolo("1234"));
            message.Message.ShouldBe("Proposta não encontrada ou Situação diferente de Pendente!");
        }

        [Fact]
        public async Task GetInserirControle_Deve_Inserir_Corretamente()
        {
            // Arrange
            var seqPessoaParaValidar = "12345";
            var proposta = await CreateRandomProposta();

            var propostas = new List<Proposta> { proposta }.AsQueryable();

            ControleImportacaoRequisicao controle = new ControleImportacaoRequisicao();
            controle.PropostaId = 1;
            controle.NumeroProtocoloRequisicao = seqPessoaParaValidar;
            controle.Status = Enums.EStatusImportacao.PENDENTE;
            await _controleRepository.InsertAsync(controle);

            _controleRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<ControleImportacaoRequisicao, bool>>>())
            .Returns((ControleImportacaoRequisicao)null);

            _propostaRepository.GetQueryableAsync().Returns(Task.FromResult(propostas));

            // Act
            await _sinPessoaAppService.GetInserirControle(seqPessoaParaValidar);

            // Assert
            await _controleRepository.Received(2).InsertAsync(Arg.Is<ControleImportacaoRequisicao>(c => c.Status == EStatusImportacao.PENDENTE));
        }

        [Fact]
        public async Task GetObterListaErros_Deve_RetornarLista_Corretamente()
        {
            // Arrange

            var controle = await CriaControle();
            var controleDto = new List<ControleImportacaoRequisicaoDto>()
                    {
                        new ControleImportacaoRequisicaoDto
                        {
                            NumeroProtocoloRequisicao = "0001",
                            PropostaId = 2,
                            Status = EStatusImportacao.ERRO,
                            Descricao=  "teste 1",
                            DataErro = DateTime.Now,
                        },
                        new ControleImportacaoRequisicaoDto
                        {
                            NumeroProtocoloRequisicao = "0002",
                            PropostaId = 2,
                            Status = EStatusImportacao.ERRO,
                            Descricao = "teste 2",
                            DataErro = DateTime.Now,
                        }
                    };

            var controleLista = controle.AsQueryable().Where(x => x.Status == EStatusImportacao.ERRO);
            _controleRepository.GetQueryableAsync().Returns(Task.FromResult(controleLista));

            _sinPessoaReqPagManager.VerificaErrosImportacao().Returns(controleLista);

            _objectMapper.Map<List<ControleImportacaoRequisicao>, List<ControleImportacaoRequisicaoDto>>(controle)
                .Returns(controleDto);

            // Act  
            var retorno = await _sinPessoaAppService.GetObterListaErros(new ViewErroRequisicaoFilterInput());

            // Assert

            retorno.ShouldNotBeNull();
            retorno.TotalCount.ShouldBe(2);
        }

        [Fact]
        public async Task UpdateAsync_Deve_Atualizar_Corretamente()
        {
            // Arrange
            string numeroProtocoloRequisicao = "0001";
            var requisicao = new ControleImportacaoRequisicao
            {
                NumeroProtocoloRequisicao = "0001",
                PropostaId = 2,
                Status = EStatusImportacao.ERRO,
                ControleImportacaoRequisicoesErro = new List<ControleImportacaoRequisicaoErro>
                    {
                        new ControleImportacaoRequisicaoErro
                        {
                            ControleImportacaoId = 1,
                            Descricao = "teste 1",
                            DataErro = DateTime.Now
                        }
                    }
            };

            _controleRepository.FirstOrDefaultAsync(Arg.Any<Expression<Func<ControleImportacaoRequisicao, bool>>>())
            .Returns(Task.FromResult(requisicao));

            _sinPessoaReqPagManager.AtualizaControleImportacaoRequisicao(numeroProtocoloRequisicao)
                .Returns(Task.FromResult(requisicao));

            // Act  
            var result = await _sinPessoaAppService.UpdateAsync(numeroProtocoloRequisicao);

            // Assert
            result.ShouldNotBeNull();
            result.ShouldBe(requisicao);

            await _controleRepository
                .Received(1)
                .UpdateAsync(requisicao);
        }

        #region CriaObjetos

        private async Task<Propostas.Proposta> CreateRandomProposta()
        {
            var unidade = new Unidade
            {
                UnidadeId = 1,
                Nome = "Tribunal Regional Federal da 3ª Região",
                CodigoTipoUnidade = ECodigoTipoUnidade.E,
                IsDeleted = false,
                NumeroCnpjCpf = "123456"
            };
            var tipoProcedimento = new Bogus.Faker<SISPREC.TiposProcedimentos.TipoProcedimento>()
                    .RuleFor(p => p.DescricaoTipoProcedimento, f => f.Random.String2(20))
                        .Generate();

            tipoProcedimento.TipoProcedimentoId = "RPV";

            var proposta = new Proposta
            {
                PropostaId = 1,
                AnoProposta = 2024,
                MesProposta = 9,
                DataAtualizacao = new DateTime(2024, 10, 3),
                ValorMinimo = 0,
                ValorMaximo = 50,
                ValorMinimoParcela = 0,
                QtdMaximaParcelaAlimetar = 0,
                QtdMaximaParcelaComum = 0,
                QtdMaximaParcelaDesapropriacaoUnico = 0,
                QtdMaximaParcelaDesapropriacao = 0,
                SituacaoProposta = ESituacaoProposta.PENDENTE,
                DataInicioCalculoJuros = new DateTime(2024, 10, 3),
                TipoProcedimentoId = tipoProcedimento.TipoProcedimentoId,
                TipoProcedimento = tipoProcedimento,
                Unidade = unidade
            };
            return proposta;
        }

        private async Task<SinPessoaReqPag> CreateRandomSinPessoa()
        {

            var faker = new Faker<SinPessoaReqPag>("pt_BR")
                .RuleFor(p => p.CodPessoaReqPag, f => f.Random.Long(1, 1000))
                .RuleFor(p => p.Nome, f => f.Name.FullName())
                .RuleFor(p => p.PessoaId, f => f.Random.Long(1, 1000));

            sinPessoaReqObj1 = faker.Generate();
            return sinPessoaReqObj1;
        }

        public async Task<List<ControleImportacaoRequisicao>> CriaControle()
        {
            var requisicao = new List<ControleImportacaoRequisicao>
            {
                new ControleImportacaoRequisicao
                {
                    NumeroProtocoloRequisicao = "0001",
                    PropostaId = 2,
                    Status = EStatusImportacao.ERRO,
                    ControleImportacaoRequisicoesErro =  new List<ControleImportacaoRequisicaoErro>
                    {
                        new ControleImportacaoRequisicaoErro
                        {
                            ControleImportacaoId = 1,
                            Descricao = "teste 1",
                            DataErro = DateTime.Now,
                        }
                    }
                },
                new ControleImportacaoRequisicao
                {
                    NumeroProtocoloRequisicao = "0002",
                    PropostaId = 2,
                    Status = EStatusImportacao.ERRO,
                    ControleImportacaoRequisicoesErro =  new List<ControleImportacaoRequisicaoErro>
                    {
                        new ControleImportacaoRequisicaoErro
                        {
                            ControleImportacaoId = 2,
                            Descricao = "teste 2",
                            DataErro = DateTime.Now,
                        }
                    }
                },
                new ControleImportacaoRequisicao
                {
                    NumeroProtocoloRequisicao = "0003",
                    PropostaId = 2,
                    Status = EStatusImportacao.CONCLUIDO
                }
            };

            await _controleRepository.InsertManyAsync(requisicao);

            return requisicao;

        }

        public async Task<RelRequisicaoProposta> CriaPropostaReqPag()
        {
            return new RelRequisicaoProposta
            {
                cod_tipo_proced = "RPV",
                cod_cadast_entida = 0,
                ano_propos = 2024,
                num_propos = 9,
                num_protoc_requis = "12345",
                cod_situac_requis_propos = "P",
                ide_propos_inicia = "",
                num_banco = 0,
                num_agenci = 0,
                num_conta_corren = "",
                cod_tipo_indica_econom = ""
            };
        }

        #endregion
    }
}
