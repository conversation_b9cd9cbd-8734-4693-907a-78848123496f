$(function () {

    $("#btnPesquisar").on('click', function () {
        resetarTabelacomparacoes();

        $('[id^="ViewModel_"]').each(function () {
            $(this).val('');
        });

        dataTable.ajax.reload();
    });

    $("#excluir").on('click', function () {
        abp.ui.block();

        alertaBotoesInvertidos('Você tem certeza?', 'Tem certeza de que deseja excluir?', 'warning')
            .then((result) => {
                if (result.isConfirmed) {
                    service.excluirJustificativa($('tr.selected').data('id'))
                        .then((response) => {
                            if (response) {
                                abp.message.success('Justificativa excluída com sucesso!');
                                $("#btnPesquisar").click();
                            }
                        })
                        .catch(() => {
                            abp.message.error('Ocorreu um erro ao excluir a justificativa.');
                        })
                        .always(() => {
                            abp.ui.unblock();
                        });
                }
                else
                    abp.ui.unblock();
            });
    });

    $('#ConsultaFilter_NumeroRequisicao').on('keypress', function (e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            $("#btnPesquisar").click();
        }
    })

    const getFilter = function () {
        const input = {};
        $("#ConsultaFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/ConsultaFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    function resetarTabelacomparacoes() {
        // Remove todos os itens da tabela
        $('.item').remove();

        // Limpa o conjunto de itens selecionados (caso ainda esteja sendo usado)
        selectedItems.clear();

        // Atualiza o campo oculto com os novos itens (caso necessário)
        atualizarCampoOculto();

        $('#deleteButton').prop('disabled', true);
    }

    const service = tRF3.sISPREC.requisicaoJustificativas.requisicaoJustificativa;
    const serviceJustificativaComparacao = tRF3.sISPREC.justificativaComparacoes.justificativaComparacao;

    const dataTable = $('#ConsultaJustificativaTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: false,
        serverSide: true,
        paging: true,
        searching: false,//disable default searchbox
        scrollY: '1px',
        scrollCollapse: true,
        order: [[5, "desc"]],
        ajax: abp.libs.datatables.createAjax(service.getList, getFilter),
        columnDefs: [
            {
                rowAction: {
                    items:
                        [
                            {
                                text: "Liberar edição",
                                action: function () {

                                    alterarEdicao(true);
                                }
                            }
                        ]
                }
            },
            {
                title: "PROCEDIMENTO",
                data: "requisicaoProtocolo.tipoProcedimentoId"
            },
            {
                title: "REQUISIÇÃO",
                data: "numeroProtocoloRequisicao"
            },
            {
                title: "TIPO ANÁLISE",
                data: "analiseTela.descricao"
            },
            {
                title: "USUÁRIO",
                data: "nomeUsuario"
            },
            {
                title: "DATA DA ANÁLISE",
                data: "dataAnalise",
                className: "text-start",
                render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss')
            },
            {
                title: "DECISÃO",
                data: "acaoJustificativa.acaoTipo.descricao"
            }
        ],
        createdRow: function (row, data) {
            // Adiciona o ID da requisição como um atributo na linha
            $(row).attr('data-id', data.requisicaoJustificativaId);
        }
    }));

    // Intercepta o evento antes da requisição
    $('#ConsultaJustificativaTable').on('preXhr.dt', function () {
        abp.ui.block({ elm: 'body', busy: true });
    });

    let requisicoesJustificativa;

    // Intercepta o evento após a requisição (sucesso ou erro)
    $('#ConsultaJustificativaTable').on('xhr.dt', function (e, settings, json) {
        requisicoesJustificativa = json.data;

        if (requisicoesJustificativa.length > 0) {
            preencherDetalhes(requisicoesJustificativa[0]);

            // Aguarda o DataTable renderizar os dados na tabela
            $('#ConsultaJustificativaTable').one('draw.dt', function () {
                $('#ConsultaJustificativaTable tbody tr:first').addClass('selected');
            });
        }

        abp.ui.unblock();
    });

    // Intercepta todas as requisições AJAX do DataTables
    $(document).ajaxError(function () {
        abp.ui.unblock(); // Garante que a UI será desbloqueada
    });

    function alterarEdicao(liberar) {
        if (liberar) {
            $("#addButton, #deleteButton, #excluir, #salvar").css("display", "unset");
            $("#ViewModel_Motivo, #ViewModel_Decisao").prop('disabled', false);
            $("#ViewModel_ComplementoMotivo, #ViewModel_Observacoes, #newItemText, #newItemTextObservacao").prop('readonly', false);
            $('#requisicoes-comparadas-table').addClass('requisicoes-comparadas-table-pointer');

        }
        else {
            $("#addButton, #deleteButton, #excluir, #salvar").css("display", "none");
            $("#ViewModel_Motivo, #ViewModel_Decisao").prop('disabled', true);
            $("#ViewModel_ComplementoMotivo, #ViewModel_Observacoes, #newItemText, #newItemTextObservacao").prop('readonly', true);
        }
    }

    function preencherDetalhes(requisicaoJustificativa) {

        $('#ViewModel_RequisicaoJustificativaId').val(requisicaoJustificativa.requisicaoJustificativaId)
        $('#ViewModel_Procedimento').val(requisicaoJustificativa.requisicaoProtocolo.tipoProcedimentoId)
        $('#ViewModel_NumeroProtocoloRequisicao').val(requisicaoJustificativa.numeroProtocoloRequisicao)
        $('#ViewModel_NomeUsuario').val(requisicaoJustificativa.nomeUsuario)
        $('#ViewModel_DataAnalise').val(requisicaoJustificativa.dataAnalise.substring(0, 19))// Pega apenas YYYY-MM-DDTHH:MM:SS
        $('#ViewModel_Decisao').val(requisicaoJustificativa.acaoJustificativa.acaoTipo.acaoTipoId).trigger('change')
        // Quando os motivos forem carregados, atribui o valor corretamente
        $(document).one('motivosCarregados', function () {
            $('#ViewModel_Motivo').val(requisicaoJustificativa.acaoJustificativaId).trigger('change');
        });
        $('#ViewModel_TipoAnalise').val(requisicaoJustificativa.analiseTela.descricao)
        $('#ViewModel_ComplementoMotivo').val(requisicaoJustificativa.complementoMotivo)
        $('#ViewModel_Observacoes').val(requisicaoJustificativa.observacoes)

        serviceJustificativaComparacao.obterJustificativaComparacaoPorRequisicaoJustificativa(requisicaoJustificativa.requisicaoJustificativaId)
            .then((response) => {

                response.forEach((item) => {
                    // Adiciona um novo item à lista com observações
                    $('#requisicoes-comparadas-table').append(`
                      <tr class="item" data-id="${itemIdCounter}">
                          <td class="numero-requisicao col-3">
                              ${item.numeroProtocoloRequisicao}
                          </td>
                          <td class="observacao">
                              ${item.observacoes || ''}
                          </td>
                      </tr>
                    `);

                    itemIdCounter++;
                });

                atualizarCampoOculto();
            })
            .catch(() => {
                abp.message.error('Ocorreu um erro ao obter justificativa comparação');
            })
            .always(() => {
                abp.ui.unblock();
            });
    }

    const serviceAcaoJustificativa = tRF3.sISPREC.acoesJustificativa.acaoJustificativa;

    $('#ViewModel_Decisao').on('change', function () {
        abp.ui.block({
            elm: '.modal-content',
            busy: true
        });
        const selectedValue = $(this).val();
        serviceAcaoJustificativa.listarAcaoJustificativa(selectedValue)
            .then((response) => {
                const $select = $('#ViewModel_Motivo');
                response.forEach(item => {
                    const option = new Option(item.descricao, item.id);
                    $select.append(option);
                });

                // Dispara um evento indicando que os motivos foram carregados
                $(document).trigger('motivosCarregados');
            })
            .catch(() => {
                abp.message.error('Ocorreu um erro ao buscar motivos');
            })
            .always(() => {
                abp.ui.unblock({
                    elm: '.modal-content'
                });
            });
    });

    let liberarEdicao = false;

    $('#ConsultaJustificativaTable').on('click', 'tr td button', function (event) {
        event.stopPropagation(); // Impede que o clique no botão acione o clique do <tr>
        liberarEdicao = true;
        $(this).closest('tr').trigger('click');
    });

    // Clique em um item da lista
    $('#ConsultaJustificativaTable').on('click', 'tr', function () {
        if ($(this).find('td.dt-empty').length > 0)
            return;

        if (!$(this).hasClass("selected")) {
            liberarEdicao = false;

            // Remove a classe 'selected' de todos os itens antes de adicionar ao novo clicado
            $('#ConsultaJustificativaTable tbody tr').removeClass('selected');

            // Adiciona a classe ao item clicado
            $(this).addClass('selected');

            let requisicaoJustificativaId = $(this).data('id');

            let requisicaoJustificativa = requisicoesJustificativa.find(item => item.requisicaoJustificativaId === requisicaoJustificativaId);

            resetarTabelacomparacoes();

            preencherDetalhes(requisicaoJustificativa);
        }

        alterarEdicao(liberarEdicao);
    });

    let itemIdCounter = 0; // Para IDs únicos de itens

    const selectedItems = new Set(); // Conjunto para armazenar os itens selecionados

    // Clique em um item da lista
    $('#requisicoes-comparadas-table').on('click', 'tr', function () {
        if (liberarEdicao) {
            const itemId = $(this).data('id'); // Identifica o item clicado
            if ($(this).hasClass('selected')) {
                $(this).removeClass('selected'); // Remove a seleção do item

                selectedItems.delete(itemId); // Remove o ID do conjunto
            } else {
                $(this).addClass('selected'); // Adiciona a classe ao item clicado
                selectedItems.add(itemId); // Adiciona o ID ao conjunto
            }

            // Habilita ou desabilita o botão com base na seleção
            $('#deleteButton').prop('disabled', selectedItems.size === 0);
        }
    });

    // Clique no botão de exclusão
    $('#deleteButton').on('click', function () {
        if (selectedItems.size > 0) {
            // Remove todos os itens selecionados
            selectedItems.forEach(id => { $(`.item[data-id = "${id}"]`).remove(); });
            selectedItems.clear(); // Limpa o conjunto de selecionados
            $(this).prop('disabled', true); // Desabilita o botão

            // Atualiza o campo oculto com os novos itens
            atualizarCampoOculto();
        }
    });

    // Função para atualizar o campo oculto com os valores dos itens da lista
    function atualizarCampoOculto() {
        // Remove todos os elementos ocultos existentes
        $('#requisicoesComparadas').empty();

        // Percorre todos os itens da lista e cria um novo elemento input oculto para cada valor
        $('#requisicoes-comparadas-table tr').each(function (index) {
            const numeroRequisicao = $(this).find('.numero-requisicao').text().trim();
            const observacoes = $(this).find('.observacao').text().trim();

            // Input oculto para o número da requisição
            const hiddenNumeroRequisicao = $('<input>')
                .attr('type', 'hidden')
                .attr('name', `ViewModel.RequisicoesComparadas[${index}].NumeroRequisicao`)
                .val(numeroRequisicao);

            // Input oculto para as observações
            const hiddenObservacoes = $('<input>')
                .attr('type', 'hidden')
                .attr('name', `ViewModel.RequisicoesComparadas[${index}].Observacoes`)
                .val(observacoes);

            // Adiciona os inputs ao formulário
            $('#requisicoesComparadas').append(hiddenNumeroRequisicao, hiddenObservacoes);
        });
    }
    // Clique no botão de adicionar
    $('#addButton').on('click', function () {
        const newItemText = $('#newItemText').val().trim();

        if (newItemText) {
            const service = tRF3.sISPREC.requisicaoJustificativas.requisicaoJustificativa;

            abp.ui.block({ elm: '.modal-content', busy: true });

            service.existeRequisicao(newItemText)
                .then((response) => {
                    if (!response) {
                        abp.message.info('Número de requisição não existe no SISPREC');
                    }
                })
                .catch((error) => {
                    abp.message.error('Ocorreu um erro ao verificar a requisição');
                })
                .always(() => {
                    abp.ui.unblock({
                        elm: '.modal-content'
                    });
                });

            const observacaoText = $('#newItemTextObservacao').val().trim();

            // Adiciona um novo item à lista com observações
            $('#requisicoes-comparadas-table').append(`
                    <tr class="item" data-id="${itemIdCounter}">
                        <td class="numero-requisicao col-3">
                            ${newItemText}
                        </td>
                        <td class="observacao">
                            ${observacaoText}
                        </td>
                    </tr>
                `);

            // Incrementa o contador de IDs únicos
            itemIdCounter++;

            // Atualiza o campo oculto com os novos itens
            atualizarCampoOculto();

            $('#newItemText').val(''); // Limpa o campo de texto
            $('#newItemTextObservacao').val(''); // Limpa o campo de observações
        } else {
            abp.message.warn('Por favor, inserir um número de requisição para o novo item.');
        }
    });

    // Permite adicionar item ao pressionar Enter
    $('.newItemInput').on('keypress', function (e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#addButton').click();
        }
    });

    $('#salvar').on('click', function (e) {
        e.preventDefault();

        abp.ui.block();

        $.post($('#formSalvar').attr('action'), $('#formSalvar').serialize())
            .done(function (data, textStatus, xhr) {
                if (xhr.status === 204) { // 204 = NoAction
                    abp.notify.success('Salvo com sucesso!');
                    alterarEdicao(false);
                    $("#btnPesquisar").click();
                } else {
                    abp.notify.error('Erro ao salvar');
                }
            })
            .fail(function () {
                abp.notify.error('Erro ao salvar');
            });

        abp.ui.unblock();
    });
});
