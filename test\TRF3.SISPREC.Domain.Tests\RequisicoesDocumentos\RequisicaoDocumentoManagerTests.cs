using NSubstitute;
using System.Linq.Expressions;
using TRF3.SISPREC.ArquivoServices;
using TRF3.SISPREC.RequisicoesDocumentos;

namespace TRF3.SISPREC.Tests.RequisicoesDocumentos;

public class RequisicaoDocumentoManagerTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
{
    private readonly IRequisicaoDocumentoRepository _repository;
    private readonly IArquivoService _arquivoService;
    private readonly RequisicaoDocumentoManager _manager;

    public RequisicaoDocumentoManagerTests()
    {
        _repository = Substitute.For<IRequisicaoDocumentoRepository>();
        _arquivoService = Substitute.For<IArquivoService>();
        _manager = new RequisicaoDocumentoManager(_repository, _arquivoService);
    }

    private RequisicaoDocumento InicializarRequisicaoDocumentoComValoresValidos()
    {
        return new Bogus.Faker<RequisicaoDocumento>()
            .RuleFor(p => p.RequisicaoDocumentoId, p => p.Random.Long(min: 1))
            .RuleFor(p => p.NomeDocumento, p => p.Random.String())
            .RuleFor(p => p.Path, p => p.Random.String())
            .RuleFor(p => p.DataCriacao, new DateTime(2025, 09, 01))
            .RuleFor(p => p.NumeroProtocoloRequisicao, p => p.Random.String())
            .RuleFor(p => p.IsDeleted, p => p.Random.Bool())
            .Generate();
    }

    [Fact]
    public async Task Deletar_Assincronamente()
    {
        var objeto = InicializarRequisicaoDocumentoComValoresValidos();
        await _manager.ExcluirAsync(objeto);
        await _repository.Received(1).DeleteAsync(objeto, Arg.Any<bool>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task Deletar_Com_Predicado()
    {
        Expression<Func<RequisicaoDocumento, bool>> predicate = requisicaoDocumento => requisicaoDocumento.RequisicaoDocumentoId == 1;//AJUSTAR ID se não for int
        await _manager.ExcluirAsync(predicate);
        await _repository.Received(1).DeleteAsync(predicate, Arg.Any<bool>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task Deletar_Direto_Com_Predicado()
    {
        Expression<Func<RequisicaoDocumento, bool>> predicate = requisicaoDocumento => requisicaoDocumento.RequisicaoDocumentoId == 1;
        await _manager.ExcluirDiretoAsync(predicate);
        await _repository.Received(1).DeleteDirectAsync(predicate, Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task Deletar_Muitos_Assincronamente()
    {
        var objeto = InicializarRequisicaoDocumentoComValoresValidos();
        var objetos = new List<RequisicaoDocumento> { objeto };
        await _manager.ExcluirMuitosAsync(objetos);
        await _repository.Received(1).DeleteManyAsync(objetos, Arg.Any<bool>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task Inserir_Muitos_Assincronamente()
    {
        var objeto = InicializarRequisicaoDocumentoComValoresValidos();
        var objetos = new List<RequisicaoDocumento> { objeto };
        await _manager.InserirMuitosAsync(objetos);
        await _repository.Received(1).InsertManyAsync(objetos, Arg.Any<bool>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task Atualizar_Muitos_Assincronamente()
    {
        var objeto = InicializarRequisicaoDocumentoComValoresValidos();
        var objetos = new List<RequisicaoDocumento> { objeto };
        await _manager.AlterarMuitosAsync(objetos);
        await _repository.Received(1).UpdateManyAsync(objetos, Arg.Any<bool>(), Arg.Any<CancellationToken>());
    }
}
