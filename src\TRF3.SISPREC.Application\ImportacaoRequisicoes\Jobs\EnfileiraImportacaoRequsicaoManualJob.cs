using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundJobs;
using TRF3.SISPREC.ControleImportacaoRequisicoes;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Settings.Importacoes;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.ImportacaoRequisicoes.Jobs
{
    [ExcludeFromCodeCoverage]
    [DisallowConcurrentExecution]
    public class EnfileiraImportacaoRequsicaoManualJob : SchedulingBackroundJob<EnfileiraImportacaoRequsicaoManualJob>, IEnfileiraImportacaoRequisicoesManualJob
    {
        private readonly IBackgroundJobManager _backgroundJobManager;
        private readonly IControleImportacaoRequisicaoRepository _controleRepository;
        public override string JobName => ImportacaoRequisicoesSettings.EnfileiraImportacaoRequisicoesManualJobName;
        public override string JobGroupName => ImportacaoRequisicoesSettings.EnfileiraImportacaoRequisicoesManualJobGroupName;

        public EnfileiraImportacaoRequsicaoManualJob(IGetLoggerService getLoggerService, IScheduler scheduler, IBackgroundJobManager backgroundJobManager, IControleImportacaoRequisicaoRepository controleRepository) : base(getLoggerService, scheduler)
        {
            _backgroundJobManager = backgroundJobManager;
            _controleRepository = controleRepository;
        }

        [UnitOfWork]
        public async override Task Execute(IJobExecutionContext context)
        {
            Logger.LogInformation(">>>> Executando Importação Manual de Requisição");

            using (_controleRepository.DisableTracking())
            {
                var propostaPraImportar = await (await _controleRepository.GetQueryableAsync()).Where(p => p.Status == Enums.EStatusImportacao.PENDENTE).ToListAsync();

                foreach (var proposta in propostaPraImportar)
                {
                    await _controleRepository.AlterarStatus(proposta.ControleImportacaoRequisicaoId, EStatusImportacao.ENQUEUE);
                    await _backgroundJobManager.EnqueueAsync(new ImportarRequisicoesManualArgs(proposta.PropostaId, proposta.NumeroProtocoloRequisicao));
                }
            }
        }

    }

}
