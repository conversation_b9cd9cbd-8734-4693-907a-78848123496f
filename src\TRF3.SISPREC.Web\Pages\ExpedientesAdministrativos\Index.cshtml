@page
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Expediente Administrativos";
    PageLayout.Content.MenuItemName = SISPRECMenus.ExpedienteAdministrativo;
}

@section scripts
{
    <abp-script src="/Pages/ExpedientesAdministrativos/index.js" />
}

<abp-card>
    <abp-card-header>

        <abp-row>
            <abp-column class="text-end">
                <abp-button id="NewExpedienteAdministrativoButton"
                            text="Novo"
                            icon="plus"
                            button-type="Primary" />
            </abp-column>
        </abp-row>

        <form asp-for="ExpedienteAdministrativoFilter" id="ExpedienteAdministrativoFilter">
            <abp-row class="d-flex justify-content-between align-items-center">
                <abp-column>
                    <abp-input asp-for="ExpedienteAdministrativoFilter.NumeroProcessoSei" />
                </abp-column>
                <abp-column>
                    <abp-select asp-for="ExpedienteAdministrativoFilter.TipoExpedienteAdministrativo" asp-items="@Model.ExpedienteAdministrativoFilter.TipoExpedienteLookupList"/>
                </abp-column>
                <abp-column>
                    <abp-input asp-for="ExpedienteAdministrativoFilter.NomeUsuario" />
                </abp-column>
                <abp-column>
                    <abp-select asp-for="ExpedienteAdministrativoFilter.StatusExpedienteAdminstrativo" asp-items="@Model.ExpedienteAdministrativoFilter.StatusLookupList" />
                </abp-column>
                <abp-column>
                    <abp-input asp-for="ExpedienteAdministrativoFilter.BlocoSisprecId" />
                </abp-column>
                <abp-column>
                    <input-requisicao-pesquisa asp-for="ExpedienteAdministrativoFilter.NumeroRequisicao" />
                </abp-column>
            </abp-row>

            <abp-row>
                <abp-column>
                    <abp-button class="float-start" button-type="Primary" text="Pesquisar" size="Small" id="btnPesquisar"></abp-button>
                </abp-column>
            </abp-row>
        </form>

    </abp-card-header>
    <abp-card-body>
        <abp-table striped-rows="true" id="ExpedienteAdministrativoTable" class="nowrap" />
    </abp-card-body>
</abp-card>

