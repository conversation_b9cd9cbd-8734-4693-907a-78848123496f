@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.ModelosDocumentos
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Modelo de Documento";
    PageLayout.Content.MenuItemName = SISPRECMenus.ModeloDocumento;
}

@section scripts
{
    <abp-script src="/Pages/ModelosDocumentos/index.js" />
}

<abp-card>
    <abp-card-header>
        <abp-row class="justify-content-between align-items-center">
            <abp-column>
                <a abp-collapse-id="ModeloDocumentoCollapse" class="text-secondary">Filtrar</a>
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="NewModeloDocumentoButton"
                            text="Novo"
                            icon="plus"
                            button-type="Primary" />
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <abp-dynamic-form abp-model="ModeloDocumentoFilter" id="ModeloDocumentoFilter" required-symbols="false" column-size="_3">
            <abp-collapse-body id="ModeloDocumentoCollapse">
                <abp-form-content />
            </abp-collapse-body>
        </abp-dynamic-form>
        <abp-table striped-rows="true" id="ModeloDocumentoTable" class="nowrap"/>
    </abp-card-body>
</abp-card>