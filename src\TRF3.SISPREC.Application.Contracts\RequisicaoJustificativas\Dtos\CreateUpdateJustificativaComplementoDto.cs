using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.RequisicaoesJustificativa;

namespace TRF3.SISPREC.RequisicaoJustificativas.Dtos
{
    public class CreateUpdateJustificativaComplementoDto
    {
        public long? RequisicaoJustificativaId { get; set; } = null;

        public string? Motivo { get; set; }

        public string? Procedimento { get; set; }

        [StringLength(RequisicaoJustificativaConsts.DES_JUSTIF_COMPLE_TAMANHO_MAX, ErrorMessage = "O Complemento do Motivo Selecionado deve ter no máximo {1} caracteres")]
        public string? ComplementoMotivo { get; set; }

        public string? Observacoes { get; set; }

        public List<string>? RequisicaoList { get; set; }

        public List<RequisicaoComparadaDto>? RequisicoesComparadas { get; set; }

        public EDescricaoAnaliseTela TipoAnalise { get; set; }
    }
}