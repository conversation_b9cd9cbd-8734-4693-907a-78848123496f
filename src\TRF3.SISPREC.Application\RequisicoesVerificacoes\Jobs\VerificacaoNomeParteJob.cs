using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundServices.Jobs;
using TRF3.SISPREC.DivergenciaNomePartes;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoNomeParteJob : AsyncBackgroundJob<VerificacaoNomeParteArgs>, IVerificacaoNomeParteJob
{
    private readonly IDivergenciaNomeParteManager _divergenciaNomeParteManager;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public VerificacaoNomeParteJob(IDivergenciaNomeParteManager divergenciaNomeParteManager, IUnitOfWorkManager unitOfWorkManager)
    {
        _divergenciaNomeParteManager = divergenciaNomeParteManager;
        _unitOfWorkManager = unitOfWorkManager;
    }

    public override async Task ExecuteAsync(VerificacaoNomeParteArgs args)
    {
        try
        {
            if (string.IsNullOrEmpty(args.NumeroProtocoloRequisicao))
            {
                Logger.LogError("Erro ao executar VerificacaoNomeParteJob. NumeroProtocoloRequisicao inválido: NumeroProtocoloRequisicao: {NumeroProtocoloRequisicao}.", args.NumeroProtocoloRequisicao);
                return;
            }

            using (var uow = _unitOfWorkManager.Begin(false, true))
            {
                await _divergenciaNomeParteManager.ProcessarVerificacaoAsync(args.NumeroProtocoloRequisicao);
                await uow.CompleteAsync();
            }

        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoNomeParteJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", args.RequisicaoVerificacaoId);
            throw;
        }
    }
}

public class VerificacaoNomeParteArgs : BaseBackgroundJobArgs
{
    public long RequisicaoVerificacaoId { get; set; }
    public string NumeroProtocoloRequisicao { get; set; }

    public override string JobGroupName => VerificacaoRequisicoesSettings.VerificacaoNomeParteJobGroupName;

    public VerificacaoNomeParteArgs(string numeroProtocoloRequisicao)
    {
        NumeroProtocoloRequisicao = numeroProtocoloRequisicao;
    }
}
