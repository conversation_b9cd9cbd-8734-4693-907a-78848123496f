using Shouldly;
using TRF3.SISPREC.EntityFrameworkCore;
using TRF3.SISPREC.ViewProcessos;
using TRF3.SISPREC.ViewProcessos.Dtos;

namespace TRF3.SISPREC.Tests.ViewProcessos;


public class ViewProcessoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly IViewProcessoAppService _appService;

    public ViewProcessoAppServiceTests()
    {
        _appService = GetRequiredService<IViewProcessoAppService>();
    }

    [Fact]
    public async Task Obter_Lista_De_Processos_Deve_Nao_Lancar_Excecao()
    {
        // Arrange
        var input = new ViewProcessoGetListInput();

        // Act & Assert
        await Should.NotThrowAsync(async () => await _appService.GetListAsync(input));
    }

    [Fact]
    public async Task Obter_Processo_Por_Id_Deve_Nao_Lancar_Excecao()
    {
        // Arrange
        //var processos = await _appService.GetListAsync(new ViewProcessoGetListInput());

        // Act & Assert
        await Should.NotThrowAsync(async () => await _appService.GetAsync(1));
    }

    [Fact]
    public async Task Filtrar_Processos_Por_Seq_Fase_Deve_Nao_Lancar_Excecao()
    {
        // Arrange
        var input = new ViewProcessoGetListInput { FaseId = 1 };

        // Act & Assert
        await Should.NotThrowAsync(async () => await _appService.GetListAsync(input));
    }

    [Fact]
    public async Task Filtrar_Processos_Por_Num_Proces_Deve_Nao_Lancar_Excecao()
    {
        // Arrange
        var input = new ViewProcessoGetListInput { NumeroProcesso = "123456" };

        // Act & Assert
        await Should.NotThrowAsync(async () => await _appService.GetListAsync(input));
    }
}
