using Microsoft.AspNetCore.Authorization;
using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.Permissoes;

[ExcludeFromCodeCoverage]
public static class SISPRECPermissoes
{
    /* Configurar permissões:
     *   - Cada permissão criada aqui, deve ser criada como "recurso" no CAU
     *   - Os recursos deverão ser atribuidos para um perfil no CAU
     *   - As permissoes criadas aqui, devem ser registradas como Policy, no método RegistrarPermissoes
     *   - Para acessar o SISPREC, os usuários deverão ter algum perfil atribuido no CAU
     *   
     *   OBS.: Para cada ambiente (desenvolvimento, homologação, etc) há um registro de sistema diferente no CAU.
     */

    //Nome da variável do objeto de autenticação do CAU que contém as Claims de permissões
    public const string NomeClaimTypePermissoes = "nome_recurso";

    private const string PermissaoGravar = ".Gravar";
    private const string PermissaoVisualizar = ".Visualizar";

    public static class Perfil
    {
        public const string Admin = "Admin";
        public const string AdminTI = "AdminTI";
    }

    public static void RegistrarPermissoes(AuthorizationOptions options)
    {
        RegistrarPermissao(options, TransmissaoCJF.Visualizar);

        RegistrarPermissao(options, MotivoAcao.Visualizar);
        RegistrarPermissao(options, MotivoAcao.Gravar);

        RegistrarPermissao(options, UnidadeOrcamentaria.Visualizar);
        RegistrarPermissao(options, UnidadeOrcamentaria.Gravar);

        RegistrarPermissao(options, Pessoa.Visualizar);
        RegistrarPermissao(options, Pessoa.Gravar);

        RegistrarPermissao(options, AdvogadoJudicial.Visualizar);
        RegistrarPermissao(options, AdvogadoJudicial.Gravar);

        RegistrarPermissao(options, RequisicaoProtocolo.Visualizar);
        RegistrarPermissao(options, RequisicaoProtocolo.Gravar);

        //Por enquanto permitir só visualizar (única ação no SISUFEP para essa tabela)
        RegistrarPermissao(options, SituacaoRequisicaoProtocolo.Visualizar);

        //Por enquanto permitir só visualizar e alterar (tal como no SISUFEP)
        RegistrarPermissao(options, Proposta.Visualizar);
        RegistrarPermissao(options, Proposta.Gravar);

        RegistrarPermissao(options, Agencia.Visualizar);
        RegistrarPermissao(options, Agencia.Gravar);

        RegistrarPermissao(options, Assunto.Visualizar);
        RegistrarPermissao(options, Assunto.Gravar);

        RegistrarPermissao(options, Banco.Visualizar);
        RegistrarPermissao(options, Banco.Gravar);

        RegistrarPermissao(options, AcaoJustificativa.Visualizar);
        RegistrarPermissao(options, AcaoJustificativa.Gravar);

        RegistrarPermissao(options, RequisicaoOcorrencia.Visualizar);
        RegistrarPermissao(options, RequisicaoOcorrencia.Gravar);

        RegistrarPermissao(options, UnidadeJudicial.Visualizar);
        RegistrarPermissao(options, UnidadeJudicial.Gravar);

        RegistrarPermissao(options, UnidadeOrcamentaria.Visualizar);
        RegistrarPermissao(options, UnidadeOrcamentaria.Gravar);

        RegistrarPermissao(options, IndiceAtualizacaoMonetaria.Visualizar);
        RegistrarPermissao(options, IndiceAtualizacaoMonetaria.Gravar);

        RegistrarPermissao(options, IndiceAtualizacaoMonetariaTipo.Visualizar);
        RegistrarPermissao(options, IndiceAtualizacaoMonetariaTipo.Gravar);

        RegistrarPermissao(options, RequisicaoParteReu.Visualizar);
        RegistrarPermissao(options, RequisicaoParteReu.Gravar);

        RegistrarPermissao(options, RequisicaoParteRequerente.Visualizar);
        RegistrarPermissao(options, RequisicaoParteRequerente.Gravar);

        RegistrarPermissao(options, RequisicaoParteAutor.Visualizar);
        RegistrarPermissao(options, RequisicaoParteAutor.Gravar);

        RegistrarPermissao(options, RequisicaoParte.Visualizar);
        RegistrarPermissao(options, RequisicaoParte.Gravar);

        RegistrarPermissao(options, RequisicaoControleCnpjCpf.Visualizar);
        RegistrarPermissao(options, RequisicaoControleCnpjCpf.Gravar);

        RegistrarPermissao(options, BeneficiarioTipo.Visualizar);
        RegistrarPermissao(options, BeneficiarioSucessaoTipo.Visualizar);
        RegistrarPermissao(options, BeneficiarioIdentificacaoTipo.Visualizar);
        RegistrarPermissao(options, DespesaClassificacao.Visualizar);
        RegistrarPermissao(options, DespesaNatureza.Visualizar);
        RegistrarPermissao(options, DespesaTipo.Visualizar);
        RegistrarPermissao(options, FaseTipo.Visualizar);
        RegistrarPermissao(options, MovimentoTipo.Visualizar);
        RegistrarPermissao(options, OrdemPagamento107aTipo.Visualizar);
        RegistrarPermissao(options, SentencaTipo.Visualizar);
        RegistrarPermissao(options, ServidorCondicaoTipo.Visualizar);
        RegistrarPermissao(options, UnidadeJudicialTipo.Visualizar);
        RegistrarPermissao(options, UnidadeJudicialTipoNatureza.Visualizar);
        RegistrarPermissao(options, UnidadeGestora.Visualizar);
        RegistrarPermissao(options, ValorTipo.Visualizar);

        RegistrarPermissao(options, ConfiguracoesAplicacao.Gravar);
    }

    public static class MotivoAcao
    {
        public const string Visualizar = nameof(MotivoAcao) + PermissaoVisualizar;
        public const string Gravar = nameof(MotivoAcao) + PermissaoGravar;
    }

    public static class UnidadeOrcamentaria
    {
        public const string Visualizar = nameof(UnidadeOrcamentaria) + PermissaoVisualizar;
        public const string Gravar = nameof(UnidadeOrcamentaria) + PermissaoGravar;
    }


    /*
     * Permissão RequisicaoProposta removida pois relacção com RequisicaoProtocolo será 1:1.
     * Para evitar mal-entendido ou confundir, usará só a permissão de RequisicaoProtocolo para ambos
     *
     */
    //public static class RequisicaoProposta
    //{
    //    public const string Visualizar = nameof(RequisicaoProposta) + PermissaoVisualizar;
    //    public const string Gravar = nameof(RequisicaoProposta) + PermissaoGravar;
    //}

    public static class Proposta
    {
        public const string Visualizar = nameof(Proposta) + PermissaoVisualizar;
        public const string Gravar = nameof(Proposta) + PermissaoGravar;
    }

    public static class SituacaoRequisicaoProtocolo
    {
        public const string Visualizar = nameof(SituacaoRequisicaoProtocolo) + PermissaoVisualizar;
        public const string Gravar = nameof(SituacaoRequisicaoProtocolo) + PermissaoGravar;
    }

    public static class RequisicaoProtocolo
    {
        public const string Visualizar = nameof(RequisicaoProtocolo) + PermissaoVisualizar;
        public const string Gravar = nameof(RequisicaoProtocolo) + PermissaoGravar;
    }

    public static class RequisicaoProcessoOrigem
    {
        public const string Visualizar = nameof(RequisicaoProcessoOrigem) + PermissaoVisualizar;
        public const string Gravar = nameof(RequisicaoProcessoOrigem) + PermissaoGravar;
    }

    public static class RequisicaoParteReu
    {
        public const string Visualizar = nameof(RequisicaoParteReu) + PermissaoVisualizar;
        public const string Gravar = nameof(RequisicaoParteReu) + PermissaoGravar;
    }

    public static class RequisicaoParteRequerente
    {
        public const string Visualizar = nameof(RequisicaoParteRequerente) + PermissaoVisualizar;
        public const string Gravar = nameof(RequisicaoParteRequerente) + PermissaoGravar;
    }

    public static class RequisicaoParteAutor
    {
        public const string Visualizar = nameof(RequisicaoParteAutor) + PermissaoVisualizar;
        public const string Gravar = nameof(RequisicaoParteAutor) + PermissaoGravar;
    }

    public static class RequisicaoParte
    {
        public const string Visualizar = nameof(RequisicaoParte) + PermissaoVisualizar;
        public const string Gravar = nameof(RequisicaoParte) + PermissaoGravar;
    }

    public static class RequisicaoControleCnpjCpf
    {
        public const string Visualizar = nameof(RequisicaoControleCnpjCpf) + PermissaoVisualizar;
        public const string Gravar = nameof(RequisicaoControleCnpjCpf) + PermissaoGravar;
    }

    public static class Pessoa
    {
        public const string Visualizar = nameof(Pessoa) + PermissaoVisualizar;
        public const string Gravar = nameof(Pessoa) + PermissaoGravar;
    }
    public static class Setor
    {
        public const string Visualizar = nameof(Setor) + PermissaoVisualizar;
        public const string Gravar = nameof(Setor) + PermissaoGravar;
    }
    public static class Blocos
    {
        public const string Visualizar = nameof(Blocos) + PermissaoVisualizar;
        public const string Gravar = nameof(Blocos) + PermissaoGravar;
    }
    public static class AdvogadoJudicial
    {
        public const string Visualizar = nameof(AdvogadoJudicial) + PermissaoVisualizar;
        public const string Gravar = nameof(AdvogadoJudicial) + PermissaoGravar;
    }

    public static class Agencia
    {
        public const string Visualizar = nameof(Agencia) + PermissaoVisualizar;
        public const string Gravar = nameof(Agencia) + PermissaoGravar;
    }

    public static class Assunto
    {
        public const string Visualizar = nameof(Assunto) + PermissaoVisualizar;
        public const string Gravar = nameof(Assunto) + PermissaoGravar;
    }

    public static class Banco
    {
        public const string Visualizar = nameof(Banco) + PermissaoVisualizar;
        public const string Gravar = nameof(Banco) + PermissaoGravar;
    }

    public static class AcaoJustificativa
    {
        public const string Visualizar = nameof(AcaoJustificativa) + PermissaoVisualizar;
        public const string Gravar = nameof(AcaoJustificativa) + PermissaoGravar;
    }

    public static class RequisicaoOcorrencia
    {
        public const string Visualizar = nameof(RequisicaoOcorrencia) + PermissaoVisualizar;
        public const string Gravar = nameof(RequisicaoOcorrencia) + PermissaoGravar;
    }

    public static class UnidadeJudicial
    {
        public const string Visualizar = nameof(UnidadeJudicial) + PermissaoVisualizar;
        public const string Gravar = nameof(UnidadeJudicial) + PermissaoGravar;
    }

    public static class IndiceAtualizacaoMonetaria
    {
        public const string Visualizar = nameof(IndiceAtualizacaoMonetaria) + PermissaoVisualizar;
        public const string Gravar = nameof(IndiceAtualizacaoMonetaria) + PermissaoGravar;
    }

    public static class IndiceAtualizacaoMonetariaTipo
    {
        public const string Visualizar = nameof(IndiceAtualizacaoMonetariaTipo) + PermissaoVisualizar;
        public const string Gravar = nameof(IndiceAtualizacaoMonetariaTipo) + PermissaoGravar;
    }

    public static class BeneficiarioTipo
    {
        public const string Visualizar = nameof(BeneficiarioTipo) + PermissaoVisualizar;
    }

    public static class BeneficiarioSucessaoTipo
    {
        public const string Visualizar = nameof(BeneficiarioSucessaoTipo) + PermissaoVisualizar;
    }

    public static class BeneficiarioIdentificacaoTipo
    {
        public const string Visualizar = nameof(BeneficiarioIdentificacaoTipo) + PermissaoVisualizar;
    }

    public static class DespesaClassificacao
    {
        public const string Visualizar = nameof(DespesaClassificacao) + PermissaoVisualizar;
    }

    public static class DespesaNatureza
    {
        public const string Visualizar = nameof(DespesaNatureza) + PermissaoVisualizar;
    }

    public static class DespesaTipo
    {
        public const string Visualizar = nameof(DespesaTipo) + PermissaoVisualizar;
    }

    public static class FaseTipo
    {
        public const string Visualizar = nameof(FaseTipo) + PermissaoVisualizar;
    }

    public static class MovimentoTipo
    {
        public const string Visualizar = nameof(MovimentoTipo) + PermissaoVisualizar;
    }

    public static class OrdemPagamento107aTipo
    {
        public const string Visualizar = nameof(OrdemPagamento107aTipo) + PermissaoVisualizar;
    }

    public static class SentencaTipo
    {
        public const string Visualizar = nameof(SentencaTipo) + PermissaoVisualizar;
    }

    public static class ServidorCondicaoTipo
    {
        public const string Visualizar = nameof(ServidorCondicaoTipo) + PermissaoVisualizar;
    }

    public static class UnidadeJudicialTipo
    {
        public const string Visualizar = nameof(UnidadeJudicialTipo) + PermissaoVisualizar;
    }

    public static class UnidadeJudicialTipoNatureza
    {
        public const string Visualizar = nameof(UnidadeJudicialTipoNatureza) + PermissaoVisualizar;
    }

    public static class UnidadeGestora
    {
        public const string Visualizar = nameof(UnidadeGestora) + PermissaoVisualizar;
    }

    public static class ValorTipo
    {
        public const string Visualizar = nameof(ValorTipo) + PermissaoVisualizar;
    }

    public static class ConfiguracoesAplicacao
    {
        public const string Gravar = nameof(ConfiguracoesAplicacao) + PermissaoGravar;
    }

    public static class TransmissaoCJF
    {
        public const string Visualizar = nameof(TransmissaoCJF) + PermissaoVisualizar;
    }

    private static void RegistrarPermissao(AuthorizationOptions options, string permissao)
    {
        options.AddPolicy(permissao, policy => policy.RequireClaim(NomeClaimTypePermissoes, permissao));
    }
}