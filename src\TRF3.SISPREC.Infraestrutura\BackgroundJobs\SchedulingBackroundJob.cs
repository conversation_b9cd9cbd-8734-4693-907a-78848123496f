using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;

namespace TRF3.SISPREC.BackgroundJobs
{
    [ExcludeFromCodeCoverage]
    public abstract class SchedulingBackroundJob<T> : BaseQuartzBackgroundJob where T : IJob
    {
        private readonly IScheduler _scheduler;

        /// <summary>
        /// Obtém o nome do trabalho.
        /// </summary>
        public abstract string JobName { get; }

        /// <summary>
        /// Obtém o nome do grupo do trabalho.
        /// </summary>
        public abstract string JobGroupName { get; }

        /// <summary>
        /// Obtém o atraso em segundos antes do início do trabalho.
        /// </summary>
        protected virtual int DelayInSeconds => 30;

        /// <summary>
        /// Obtém o intervalo em segundos entre as execuções do trabalho.
        /// </summary>
        protected virtual int IntervalInSeconds => 40;

        /// <summary>
        /// Inicializa uma nova instância da classe <see cref="SchedulingBackroundJob{T}"/>.
        /// </summary>
        /// <param name="getLoggerService">O serviço de logger a ser usado para registro.</param>
        /// <param name="scheduler">A instância do agendador a ser usada para agendar tarefas.</param>
        public SchedulingBackroundJob(IGetLoggerService getLoggerService, IScheduler scheduler) : base(getLoggerService)
        {
            _scheduler = scheduler;
        }

        /// <summary>
        /// Agenda o trabalho com base nas configurações fornecidas.
        /// </summary>
        public virtual void Agendar()
        {
            var jobDetail = JobBuilder
                .Create<T>()
                .WithIdentity(JobName, JobGroupName)
                .Build();

            var trigger = TriggerBuilder.Create()
                .WithIdentity(JobName, JobGroupName)
                .StartAt(DateTimeOffset.UtcNow.AddSeconds(DelayInSeconds))
                .WithSimpleSchedule(x => x
                    .WithIntervalInSeconds(IntervalInSeconds)
                    .RepeatForever()
                    //.WithMisfireHandlingInstructionIgnoreMisfires())
                    .WithMisfireHandlingInstructionNextWithExistingCount())
                .Build();

            _scheduler.ScheduleJob(jobDetail, new List<ITrigger>() { trigger }, true).Wait();
        }
    }
}
