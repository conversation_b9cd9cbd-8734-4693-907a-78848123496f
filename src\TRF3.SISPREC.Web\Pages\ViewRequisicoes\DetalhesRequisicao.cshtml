@page
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using TRF3.SISPREC.Localization
@using Microsoft.AspNetCore.Mvc.Localization
@using TRF3.SISPREC.Web.Views.Shared.Components;
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@model TRF3.SISPREC.Web.Pages.ViewRequisicoes.DetalheModalModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Detalhes da Requisição";
}

@section scripts
{
	<abp-script src="/js/componente-utils.js" />
    <abp-script src="/js/exportacao-pdf.js" />
    <script type="module" src="/Pages/ViewRequisicoes/detalhesRequisicao.js"></script>
}
@section styles
{
    <abp-style src="/Pages/ViewRequisicoes/index.css" />
}

<abp-card>
    <abp-card-header>
        <abp-row class="d-flex justify-content-between align-items-center">
            <abp-column size="_2">
				<input-requisicao-pesquisa asp-for="ConsultaModel.NumeroProtocoloRequisicao"/>
            </abp-column>

            <abp-column>
                <abp-button button-type="Primary" type="submit" id="btnPesquisar">Pesquisar</abp-button>
            </abp-column>

            <abp-column class="col-2" style="text-align: right;">
                <abp-button size="Small" id="btnExportarPDF" class="btn-primary-outline custom-border mx-1" block="true">Exportar PDF</abp-button>
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <abp-tabs>
            <abp-tab title="Consulta">
                <partial name="PartialViews/_detalhesRequisicaoConsultaPartial" />
            </abp-tab>
            <abp-tab title="Por CPF/CNPJ">
                <partial name="PartialViews/_detalhesRequisicaoRequerentePartial" />
            </abp-tab>
            <abp-tab title="Por Originário">
                <partial name="PartialViews/_detalhesRequisicaoPorOriginariosPartial" />
            </abp-tab>

        </abp-tabs>

        <hr />

        <abp-tabs>
            <abp-tab title="Requerente / Honorários">
                <partial name="PartialViews/_detalhesRequisicaoRequerenteHonorariosPartial" />
            </abp-tab>

            <abp-tab name="autorExpediente" title="Autor / Expediente">
                <partial name="PartialViews/_detalhesRequisicaoAutorExpedientePartial" />
            </abp-tab>

            <abp-tab name="requeridoCompensacao" title="Requerido / Compensação">
                <partial name="PartialViews/_detalhesRequisicaoRequeridoCompensacaoPartial" />
            </abp-tab>

            <abp-tab name="originarios" title="Originários">
                <partial name="PartialViews/_detalhesRequisicaoOriginariosPartial" />
            </abp-tab>

            <abp-tab name="irPss" title="I.Renda / PSS">
                <partial name="PartialViews/_detalhesRequisicaoIrPssPartial" />
            </abp-tab>

            <abp-tab name="ocorrencias" title="Ocorrências">
                <partial name="PartialViews/_detalhesRequisicaoOcorrenciasPartial" />
            </abp-tab>

            <abp-tab name="referReinclusao" title="Refer./Reinclusão">
                <partial name="PartialViews/_detalhesRequisicaoReinclusao.cshtml" />
            </abp-tab>

            <abp-tab name="consultaCpf" title="Consulta CPF">
                <partial name="PartialViews/_detalhesRequisicaoConsultaCpfPartial" />
            </abp-tab>
        </abp-tabs>
    </abp-card-body>
</abp-card>
